import fg from 'fast-glob';
import fs from 'fs';
import path from 'path';

export default async function generateThemesFile() {
  // Step 1: 使用 fast-glob 查找所有 .mjs 文件
  const mjsFiles = await fg('./node_modules/shiki/dist/themes/*.mjs');

  // Step 2: 提取文件名（去掉 .mjs 后缀）
  const themes = mjsFiles.map(filePath => path.basename(filePath, '.mjs'));

  // Step 3: 生成 TypeScript 文件内容
  const tsContent = `
        // This file is generated by 'scripts/get-themes.ts'
        const themes = ${JSON.stringify(themes, null, 4)} as const;

        export type ShikiTheme = typeof themes[number];
        export { themes };
    `.trim();

  // Step 4: 将内容写入 ./src/constants/themes.ts 文件
  const outputPath = path.resolve('./src/constants/themes.ts');
  fs.writeFileSync(outputPath, tsContent);

  console.log(`Themes have been written to ${outputPath}`);
}

// 调用函数生成 themes.ts 文件
// generateThemesFile();