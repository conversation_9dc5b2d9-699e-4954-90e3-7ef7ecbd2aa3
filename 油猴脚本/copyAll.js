import fs from 'fs';
import path from 'path';

// 递归创建目标目录，返回是否创建成功或已存在
function ensureDirectoryExistence(dirPath) {
  if (!fs.existsSync(dirPath)) {
    try {
      fs.mkdirSync(dirPath, { recursive: true });
      return true;
    } catch (error) {
      console.error(`Failed to create directory: ${dirPath}`);
      console.error(error);
      return false;
    }
  }
  return true; // 目录已存在
}

// 复制文件
function copyFiles(sourceDir, targetDir) {
  // 确保目标目录存在，如果不存在则跳过复制操作
  if (!ensureDirectoryExistence(targetDir)) {
    console.log(`Skipping copying from ${sourceDir} to ${targetDir} due to directory creation failure.`);
    return;
  }

  // 读取源目录中的所有文件和子目录
  const items = fs.readdirSync(sourceDir);

  // 遍历所有文件和目录
  items.forEach(item => {
    const sourcePath = path.join(sourceDir, item);
    const targetPath = path.join(targetDir, item);

    // 检查是否为目录
    if (fs.lstatSync(sourcePath).isDirectory()) {
      // 如果是目录，递归复制
      copyFiles(sourcePath, targetPath);
    } else {
      // 如果是文件，复制并替换
      fs.copyFileSync(sourcePath, targetPath);
      // console.log(`Copied: ${sourcePath} -> ${targetPath}`);
    }
  });
}

// 执行复制操作的函数
export default function main() {
  // 定义需要复制的路径
  const operations = [
    {
      source: path.join('./', 'node_modules', 'gs-bim-air', 'public', 'js'),
      target: path.join('./', 'public', 'js'),
    },
    {
      source: path.join('./', 'node_modules', 'gs-bim-air', 'lib'),
      target: path.join('./', 'public', 'lib'),
    },
  ];

  // 执行所有复制操作
  operations.forEach(operation => {
    if (fs.existsSync(operation.source)) {
      console.log(`Copying from ${operation.source} to ${operation.target}`);
      copyFiles(operation.source, operation.target);
    } else {
      console.log(`Source directory does not exist: ${operation.source}. Skipping.`);
    }
  });

  console.log('All operations completed!');
}

// // 执行主函数
// main();
