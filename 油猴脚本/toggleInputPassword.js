// ==UserScript==
// @name         toggleInputPassword
// @namespace    http://tampermonkey.net/
// @version      1.7.3
// @description  显示登录页账号密码
// <AUTHOR>
// @license MIT
// @match        */account/login

// @require      https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js
// @grant        GM_addStyle
// @downloadURL https://update.greasyfork.org/scripts/516785/autoFillCredentials%20-%20jquery.user.js
// @updateURL https://update.greasyfork.org/scripts/516785/autoFillCredentials%20-%20jquery.meta.js
// ==/UserScript==


(function ($, JSEncrypt) {
  'use strict';

  const autoFillCredentials = {
    datas: {
      showPassword: false,
      pubKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeVp+B5QC2io6RJYx5NtMryiE02yIRVi0Ead2Oaklfo6cc/vBoSh35oxVDA2WSfgP5JwZ74DqRHFHt5LbXVnR//CebiHrvaFfcF31TNIriDtvitGqGaONbzRSYjI0t4OVHUtR1MGXtD+yCuXnLnZJE8Rnn+c5YHROG+AH3gV6t1wIDAQAB',
    },
    init() {

      this.render();
      this.bindEvents();

window.addEventListener('popstate', function(event) {
    if (window.location.pathname?.includes('/account/login')) {
        this.$toggle_btn?.show?.();
    } else {
        this.$toggle_btn?.hide?.();
    }
    console.log('路由变化:', window.location.pathname);
});

    },
    render() {

      if (!$('form').length) {
        setTimeout(() => {
          console.log('重新获取')
          this.init();
        }, 600);
      }

      // 表单控件的样式
      this.$input_username = $('form input');
      this.$input_password = $('form input[type="password"]');

      if (this.$toggle_btn?.remove) {
        this.$toggle_btn?.remove?.()
      }

      // 创建按钮元素
      this.$toggle_btn = $('<button class="toggle-input-password-btn-afki789qw">');
      this.$toggle_btn.text(this.datas.showPassword ? '隐藏密码' : '显示密码'); // 设置按钮显示的文本
      console.log(this)
      // 将按钮插入到body元素中
      $('body').append(this.$toggle_btn);
    },
    bindEvents() {
      const me = this;
      me.$toggle_btn.on('click', this.handleToggleInputType.bind(this))
    },
    handleToggleInputType() {
      const me = this;
      console.log(me.datas.showPassword, me.$input_password)
      if (!me.$input_password.length) {
        me.render();
        me.bindEvents();
        me.handleToggleInputType.bind(me);
        return
      }
      console.log(me.datas.showPassword, me)
      me.datas.showPassword = !me.datas.showPassword;
      me.$toggle_btn.text(me.datas.showPassword ? '隐藏密码' : '显示密码');
      me.$input_password.attr('type', me.datas.showPassword ? 'text' : 'password')
    },
  }

  autoFillCredentials.init()

  GM_addStyle(`
    body {
      font-family: Fira Code, Consolas !important;
      overflow: hidden;
    }

    .toggle-input-password-btn-afki789qw:hover {
      right: 0;
      padding-left: 30px;
    }

    .toggle-input-password-btn-afki789qw {
      position: absolute;
      right: -100px;
      bottom: 10%;
      z-index: 10;
      background: #41aefb4f;
      color: #ffffff;
      font-size: 12px;
      padding: 17px 30px 15px 60px;
      line-height: 12px;
      cursor: pointer;
      box-shadow: none;
      border-radius: 27px 0 0 27px;
      box-shadow: #00000038 -18px -5px 10px;
      transition: all .5s;
    }
  `);

})(jQuery, JSEncrypt);
