var lei = window.lei = {};

lei.common = {
  init: function () {
    // 一些公共方法的初始化
    this.getUserInfo();
  },
  getUserInfo: function () {
    // Do something
  }
}

lei.validateStr = {
  tel: /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1})|(17[0-9]{1})|(14[0-9]{1}))+\d{8})$/,
  email: /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/,
  password: /^.{6,16}$/,
  ip: /^(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[0-9]{1,2})(\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[0-9]{1,2})){3}$/,
  qq: /^[1-9][0-9]{4,9}$/,
  code: /^[0-9]{6}$/
}

lei.datas = {
  id: 1
}

lei.prototype.index = {
  init: function () {
    // 初始化公共方法
    lei.common.init();
    // 初始化一些其他的操作
    // Do something
  }
}

// 使用：
new lei().index.init();