<!DOCTYPE html>
<html>
<header>
  <meta charset="utf-8">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/css/bootstrap.min.css" />
  <style>
    body {
      font-family: Fira Code, Consolas !important;
    }

    .btn-primary {
      --bs-btn-bg: #FF5722;
      --bs-btn-border-color: #FF5722;
      --bs-btn-hover-bg: #FF5722;
      --bs-btn-hover-border-color: #FF5722;
      --bs-btn-active-bg: #FF5722;
      --bs-btn-active-border-color: #FF5722;
      --bs-btn-disabled-bg: #ff57224d;
      --bs-btn-disabled-border-color: #ff57224d;
    }

    .list-group-item {
      cursor: pointer;
      white-space: nowrap;
      font-family: Fira Code, Consolas;
      font-size: 14px;
      --bs-list-group-active-bg: #ff5722;
    }

    .list-group-item::before {
      content: "";
      display: inline-block;
      margin-right: 10px;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: #000;
      vertical-align: 2px;
    }


    .list-group-item.active::before {
      background-color: #fff;
    }


    pre {
      white-space: normal;
      font-family: Fira Code, Consolas !important;
      font-size: 14px;
      font-weight: 300;
      line-height: 26px;
    }

    .form-container {
      box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017;
    }
  </style>
</header>

<body>
  <div>
    <form action="dologin" method="post" class="container form-container mt-5 mb-5 p-4 border-0 rounded  shadow">
      <div>
        <span class="form-label me-2">用户名：</span>
        <input class="form-control mb-3" id="j_username" name="j_username" type="text"></input>
      </div>
      <div>
        <span class="form-label me-2">密码：</span>
        <input class="form-control mb-3" id="pwd" name="j_password" type="password"></input>
      </div>
      <div>
        <button class="btn btn-primary w-100" type="submit">登录</button>
      </div>
    </form>
  </div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <!-- 复制按钮 -->
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-clipboard"
    viewBox="0 0 16 16">
    <path
      d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1z" />
    <path
      d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0z" />
  </svg>

  <!-- 成功 -->
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check2"
    viewBox="0 0 16 16">
    <path
      d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0" />
  </svg>

  <script>


    function copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          console.log("复制成功!");
        })
        .catch((err) => {
          console.error("复制失败: ", err);
        });
    }

  </script>
</body>

</html>