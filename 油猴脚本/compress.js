import archiver from 'archiver';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前脚本的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 解析命令行参数
const args = process.argv.slice(2);
const wrapInDir = args.includes('--wrap'); // 检查是否有 --wrap 参数

// 获取当前时间戳
const timestamp = Date.now();

// 压缩文件名
const zipFileName = `DSX_${timestamp}_T.zip`;

// 定义目录路径
const distDir = path.join(__dirname, '../dist');
const zipFilePath = path.join(__dirname, zipFileName);
const bundleListDir = path.join(__dirname, '../BUNDLE_LIST'); // 指定 BUNDLE_LIST 在上一级目录
const targetZipPath = path.join(bundleListDir, zipFileName);

// 计算相对路径
const relativePath = path.relative(__dirname, targetZipPath);

// 确保 BUNDLE_LIST 目录存在（如果不存在则创建）
if (!fs.existsSync(bundleListDir)) {
  fs.mkdirSync(bundleListDir, { recursive: true });
}

// 创建输出流
const output = fs.createWriteStream(zipFilePath);
const archive = archiver('zip', {
  zlib: { level: 9 } // 设置压缩级别
});

// 监听完成事件
output.on('close', () => {
  // 打印目录结构
  console.log('\n压缩包中的目录结构:');
  printTree(distDir, 0);

  console.log(`压缩完成，共 ${archive.pointer()} 字节`);

  // 移动压缩文件到 BUNDLE_LIST 目录
  fs.rename(zipFilePath, targetZipPath, (err) => {
    if (err) throw err;
    console.log(`文件已移动到: ${relativePath}`);

  });
});

// 监听警告事件
archive.on('warning', (err) => {
  if (err.code === 'ENOENT') {
    console.warn(`警告: ${err.message}`);
  } else {
    throw err;
  }
});

// 监听错误事件
archive.on('error', (err) => {
  throw err;
});

// 将输出流绑定到压缩实例
archive.pipe(output);

// 添加目录到压缩包
if (wrapInDir) {
  archive.directory(distDir, 'dist'); // 包裹一层目录
} else {
  archive.directory(distDir, false); // 不包裹目录
}

// 结束压缩过程
archive.finalize();

// 递归函数来打印目录结构
function printTree(dirPath, indentLevel) {
  const files = fs.readdirSync(dirPath);

  files.forEach((file, index) => {
    const fullPath = path.join(dirPath, file);
    const isLast = index === files.length - 1;
    const isDirectory = fs.lstatSync(fullPath).isDirectory();

    const prefix = indentLevel > 0 ? (isLast ? '└── ' : '├── ') : '';
    const indent = '   '.repeat(indentLevel);

    console.log(`${indent}${prefix}${file}`);

    if (isDirectory) {
      printTree(fullPath, indentLevel + 1);
    }
  });
}
