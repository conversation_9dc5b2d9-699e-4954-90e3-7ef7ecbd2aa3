import fs from 'fs';
import os from 'os';
import path from 'path';
import { fileURLToPath } from 'url';

// 公司里面 嵌入乾坤的子应用，run start 之前自动变更HOST为当前ip

// 获取 __dirname
const isESModule = typeof __dirname === 'undefined';

let resolvedDirname;

if (isESModule) {
  const __filename = fileURLToPath(import.meta.url);
  resolvedDirname = path.dirname(__filename);
} else {
  resolvedDirname = __dirname;
}

// 获取本机 IP 地址的函数
function getLocalIP() {
  const interfaces = os.networkInterfaces();

  for (const ifaceName in interfaces) {
    for (const iface of interfaces[ifaceName]) {
      // 检查是否是 IPv4 地址且非本地回环地址
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }

  return '127.0.0.1'; // 如果没有找到合适的 IP，使用回环地址
}

function writeIPToEnvFile() {
  const localIP = getLocalIP();

  // 设置 .env 文件路径，指向上一级目录
  const envFilePath = path.resolve(resolvedDirname, '../.env'); // 假设 .env 文件在上一层目录

  // 要写入的内容
  const envContent = `PORT = 3020\nHOST = ${localIP}\n`;

  // 写入或覆盖 .env 文件中的 HOST 变量
  fs.writeFile(envFilePath, envContent, 'utf8', (err) => {
    if (err) {
      console.error('写入 .env 文件时出错:', err);
    } else {
      console.log('.env 文件更新成功:', envContent);
    }
  });
}

// 执行写入操作
writeIPToEnvFile();
