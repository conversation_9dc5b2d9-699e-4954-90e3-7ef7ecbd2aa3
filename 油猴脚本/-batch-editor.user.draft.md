<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-04 13:47:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-04 13:47:10
 * @FilePath: /油猴脚本/-batch-editor.user.od.md
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
根目下建一个菜单数据批量修改的油猴脚本
1. 主题为：Retro-Futurism 复古未来主义。prompt为：retro-futuristic city, flying cars, 1950s sci-fi style, ray guns, vintage tech aesthetic --v 5 --ar 16:9

2. 获取菜单的接口需要根据用户类型确定菜单接口优先级

3. 管理员优先使用接口：`${baseUrl}/dxdsapi/PrivilegeProduct/privilegeTree/1?projectCode=1`

4. 普通用户优先使用接口：`${baseUrl}/dxdsapi/PrivilegeProduct/myPrivilege/1`

5. 菜单数据结构的核心字段如下：

| 字段名 | 类型 | 含义 | 示例值 |
|--------|------|------|--------|
| id | String/Number | 菜单项唯一标识符 | "1001" |
| name | String | 菜单项显示名称 | "用户管理" |
| code | String | 菜单项编码 | "user-management" |
| children | Array | 子菜单项数组 | [{...}, {...}] |
|--------|------|------|--------|
| routingUrl | String | 路由地址（最高优先级） | "/admin/user" |
| activeRule | String | 资源地址/权限验证路径 | "/api/user/*" |
| activeRule2 | String | 辅助资源地址 | "/user/list" |
| url | String | URL地址 | "https://example.com/user" |
| path | String | 路径 | "/user" |
|--------|------|------|--------|
| displayLocation | String | 交互方式/打开方式 | _micro（微前端）<br/>_iframe（iframe）<br/>_blank（新窗口）<br/>_self（当前窗口） |
| iconClass | String | 菜单图标CSS类名 | "fa fa-user" |
| sortNo | Number/String | 排序号 | 10 |
|--------|------|------|--------|
| menu | Boolean | 是否为菜单项 | true |
| deleteFlag | Boolean | 是否已删除/隐藏 | false |
| commonModule | String | 是否为常用模块 | "0"（否）/ "1"（是） |
| injectToken | Boolean | 是否注入Token | false |
| checkPermission | Boolean | 是否需要权限验证 | true |
|--------|------|------|
| memo | String | 备注说明 |


6. 检查是否为管理员，通过序列化获取localStorage中的user。其中id为'1'或roleList中包含code为'administrator'的角色 为管理员。

7. 界面默认是一个设置图标，悬浮于右下角。点击设置。出现菜单配置界面并隐藏图标。菜单界面右上角有关闭按钮，点击之后关闭界面，重新显示设置按钮。

8. 菜单配置界面为两个模块顶部为菜单，以及搜索。菜单搜索的下方为横向的三栏，左侧为模块列表（菜单数据的第一层），中间为菜单配置（模块列表选中的项 之下的剩余的数据结构），右侧为菜单配置的详细信息。

9. menu: false - 不是菜单项的资源

10. code包含特定前缀 - 按钮、链接等组件：
  [button]- - 按钮
  [link]- - 链接
  [tab]- - 标签页
  [component]- - 组件


11. deleteFlag: true - 已删除项目（可通过开关控制显示）

12. 操作模式总结
| 操作方式 | 触发条件 | 行为描述 | 视觉效果 |
|---------|---------|---------|---------|
| 单击 | 普通点击 | 单选模式，选中当前项 | 粉红色高亮 |
| Ctrl+单击 | Ctrl+点击 | 多选模式，切换选择状态 | 绿色高亮 |
| 双击 | 连续两次点击 | 显示菜单项详情弹框 | 弹框显示 |
| 拖拽 | 按住+移动 | 连续选择多个菜单项 | 绿色高亮 |
| Ctrl+拖拽 | Ctrl+按住+移动 | 连续切换多个菜单项状态 | 绿色/取消高亮 |