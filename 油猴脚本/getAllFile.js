import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';

function copyDirectory(src, dest) {
  // 确保目标目录存在
  fs.mkdirSync(dest, { recursive: true });

  // 读取源目录中的所有文件和文件夹
  const items = fs.readdirSync(src);

  items.forEach(item => {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);

    // 获取当前项的信息
    const stat = fs.statSync(srcPath);

    if (stat.isDirectory()) {
      // 如果是文件夹，递归拷贝
      copyDirectory(srcPath, destPath);
    } else {
      // 如果是文件，直接拷贝
      fs.copyFileSync(srcPath, destPath);
    }
  });
}


/**
 * 递归获取目录结构及图片的宽高
 * @param {string} dirPath - 目录路径
 * @returns {Promise<object>} - 树形结构
 */
async function getDirectoryTree(dirPath) {
  const result = {
    id: uuidv4(), // 生成唯一 ID
    name: path.basename(dirPath),
    type: 'folder',
    path: dirPath,
    children: []
  };

  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stats = fs.statSync(itemPath);

    if (stats.isDirectory()) {
      // 如果是目录，递归获取其内容
      result.children.push(await getDirectoryTree(itemPath));
    } else {
      // 如果是文件，添加文件信息
      const fileExt = path.extname(item).toLowerCase();
      const fileInfo = {
        id: uuidv4(), // 生成唯一 ID
        name: item,
        type: 'file',
        path: itemPath
      };

      // 检查是否是图片文件
      if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'].includes(fileExt)) {
        try {
          const { width, height } = await sharp(itemPath).metadata();
          fileInfo.width = width;
          fileInfo.height = height;
        } catch (error) {
          console.error(`无法获取 ${itemPath} 的尺寸:`, error.message);
        }
      }

      result.children.push(fileInfo);
    }
  }

  return result;
}

// 使用示例
const dirPath = 'D:/BaiduNetdiskDownload/【jpg】压缩包/dandadang'; // 替换为你的目录路径


const destinationDirectory = './public/dandadang'; // 替换为目标目录的路径

copyDirectory(dirPath, destinationDirectory);

console.log('拷贝完成');

getDirectoryTree(destinationDirectory)
  .then((directoryTree) => {
    // 将结果写入 dandadang.ts
    const tsContent = `export const dandadang = [${JSON.stringify(directoryTree, null, 2)}];\n`;
    fs.writeFileSync('./src/services/dandadang.ts', tsContent, 'utf-8');
    console.log('目录结构及图片尺寸已写入 dandadang.ts');
  })
  .catch((error) => {
    console.error('发生错误:', error.message);
  });
