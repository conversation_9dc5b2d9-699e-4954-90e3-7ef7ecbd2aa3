请在项目根目录下创建一个用于批量修改菜单数据的Tampermonkey（油猴）用户脚本，具体要求如下：

## 1. 脚本基本信息
- **主题风格**：Cyberpunk（赛博朋克）
- **设计提示词**：cyberpunk style, neon lights, matrix aesthetic, dark background, glowing effects, high-tech interface, digital rain, holographic elements --v 5 --ar 16:9
- **文件位置**：项目根目录
- **文件名**：menu-batch-editor.user.js

## 2. API接口优先级策略
根据用户权限类型自动选择合适的菜单数据接口：

**管理员用户**（优先级1）：
```
${baseUrl}/dxdsapi/PrivilegeProduct/privilegeTree/1?projectCode=1
```

**普通用户**（优先级2）：
```
${baseUrl}/dxdsapi/PrivilegeProduct/myPrivilege/1
```

## 3. 用户权限判断逻辑
从 `localStorage` 中获取用户信息并判断管理员权限：
- 解析 `localStorage.user` 中的用户数据
- **管理员条件**（满足任一即可）：
  - `user.id === '1'`
  - `user.roleList` 中存在 `code === 'administrator'` 的角色

## 4. 菜单数据结构定义
### 核心标识字段
| 字段名 | 类型 | 含义 | 示例值 |
|--------|------|------|--------|
| id | String/Number | 菜单项唯一标识符 | "1001" |
| name | String | 菜单项显示名称 | "用户管理" |
| code | String | 菜单项编码 | "user-management" |
| children | Array | 子菜单项数组 | [{...}, {...}] |

### 路由相关字段（按优先级排序）
| 字段名 | 优先级 | 含义 | 示例值 |
|--------|--------|------|--------|
| routingUrl | 1 | 路由地址（最高优先级） | "/admin/user" |
| activeRule | 2 | 资源地址/权限验证路径 | "/api/user/*" |
| activeRule2 | 3 | 辅助资源地址 | "/user/list" |
| url | 4 | URL地址 | "https://example.com/user" |
| path | 5 | 路径 | "/user" |

### 显示配置字段
| 字段名 | 类型 | 含义 | 可选值 |
|--------|------|------|--------|
| displayLocation | String | 交互方式/打开方式 | `_micro`（微前端）<br/>`_iframe`（iframe）<br/>`_blank`（新窗口）<br/>`_self`（当前窗口） |
| iconClass | String | 菜单图标CSS类名 | "fa fa-user" |
| sortNo | Number/String | 排序号 | 10 |

### 状态控制字段
| 字段名 | 类型 | 含义 | 可选值 |
|--------|------|------|--------|
| menu | Boolean | 是否为菜单项 | `true`（菜单项）<br/>`false`（非菜单资源） |
| deleteFlag | Boolean | 是否已删除/隐藏 | `true`（已删除）<br/>`false`（正常） |
| commonModule | String | 是否为常用模块 | "0"（否）<br/>"1"（是） |
| injectToken | Boolean | 是否注入Token | `true`/`false` |
| checkPermission | Boolean | 是否需要权限验证 | `true`/`false` |
| memo | String | 备注说明 | 任意文本 |

## 5. 特殊项目识别规则
### 非菜单项识别
- `menu: false` - 标识为资源项而非菜单项

### 组件类型识别（通过code前缀）
| 前缀 | 类型 | 示例 |
|------|------|------|
| `[button]-` | 按钮组件 | `[button]-save` |
| `[link]-` | 链接组件 | `[link]-external` |
| `[tab]-` | 标签页组件 | `[tab]-settings` |
| `[component]-` | 通用组件 | `[component]-widget` |

### 删除项目处理
- `deleteFlag: true` - 已删除项目（可通过界面开关控制是否显示）

## 6. 用户界面设计要求
### 主入口
- **默认状态**：右下角悬浮设置图标（复古未来主义风格）
- **激活状态**：点击图标后显示菜单配置界面，同时隐藏设置图标

### 主界面布局（赛博朋克紧致设计 - 优化版）
```
╔═══════════════════════════════════════════════════════════════╗
║ ▲ CYBER MENU MATRIX v2.0.77                    [↻][RST][✕] ║  
╠═══════════════════════════════════════════════════════════════╣
║ 🔍 [░░░░░░░░░░░░SEARCH░░░░░░░░░░░░] [SCAN] │ 📊[1001] ◤LOG◢ ║
╠═══════════════════════════════════════════════════════════════╣
║ ┌─MODULE TREE──────────┐ ┌─MENU LIST (VERTICAL)─────────────┐ ║
║ │ ▣ SYS               │ │ ▣USER-MGMT     [●] ID:1001 _micro│ ║
║ │ ├─▫Auth             │ │ ▣AUTH-SERVICE  [○] ID:2003 _self │ ║
║ │ ├─▪Data             │ │ ▣DATA-CONFIG   [●] ID:3007 _iframe│ ║
║ │ └─▪Logs             │ │ ▣CONF-PANEL    [○] ID:4012 _blank│ ║
║ │ ▣ ADMIN             │ │ ▣LOG-VIEWER    [●] ID:5008 _micro│ ║
║ │ ├─▫Users            │ │ ▣REPORT-SYS    [○] ID:6015 _self │ ║
║ │ └─▪Roles            │ │ ▣BACKUP-TOOL   [✕] ID:7022 _self │ ║
║ │                     │ │ ▣MONITOR-DASH  [●] ID:8030 _micro│ ║
║ └─────────────────────┘ └─────────────────────────────────────┘ ║
╠═══════════════════════════════════════════════════════════════╣
║ ┌─SELECTED INFO────────────────────────────────────────────────┐ ║
║ │ █ USER-MGMT │ activeRule: /api/user/* │ Route: /admin/user │ ║
║ │ █ MULTI-SELECT MODE │ Selected: 3 items │ Ready for batch   │ ║
║ └─────────────────────────────────────────────────────────────┘ ║
╠═══════════════════════════════════════════════════════════════╣
║ activeRule: [░░░░░░░IP/DOMAIN░░░░░░░] [SET] [COPY] │ 💾[AUTO] ║
╚═══════════════════════════════════════════════════════════════╝
```

### 赛博朋克设计语言
#### 🌈 色彩系统
| 元素 | 颜色代码 | 用途 |
|------|----------|------|
| **主色** | `#00FFFF` (霓虹青) | 边框、文字 |
| **辅色** | `#FF006E` (霓虹粉) | 选中状态 |
| **强调** | `#39FF14` (矩阵绿) | 多选、激活 |
| **警告** | `#FFD700` (金黄) | 警告状态 |
| **背景** | `#0A0A0A` (深黑) | 主背景 |
| **面板** | `#1A1A2E` (深蓝黑) | 面板背景 |

#### ⚡ 视觉效果
- **发光边框**：`box-shadow: 0 0 10px currentColor`
- **扫描线**：动态横向扫描线效果
- **矩阵雨**：背景数字雨动画
- **全息投影**：半透明叠加效果
- **故障效果**：文字偶发故障闪烁

#### 🔧 菜单节点卡片（精简版）
```
▣USER          ▣AUTH          ▣DATA
1001           2003           3007  
[●]ACTIVE      [○]PAUSE       [●]ACTIVE
_micro         _iframe        _self
```

#### 🎯 状态指示器
| 符号 | 含义 | 颜色 |
|------|------|------|
| `▣` | 菜单节点 | 霓虹青 |
| `[●]` | 激活状态 | 矩阵绿 |
| `[○]` | 暂停状态 | 灰色 |
| `[✕]` | 删除状态 | 红色 |
| `→` | 滚动指示 | 霓虹青 |

### 交互操作模式（赛博朋克版）
| 操作 | 视觉反馈 | 效果描述 |
|------|----------|----------|
| **点击** | 粉色霓虹光环 | 单选模式 |
| **Ctrl+点击** | 绿色矩阵脉冲 | 多选切换 |
| **双击** | 全息弹窗 | 详情编辑 |
| **拖拽** | 激光连线 | 范围选择 |
| **悬停** | 扫描线动画 | 预览效果 |

## 7. 功能要求（赛博朋克版）
- 支持菜单数据的批量编辑和修改
- 支持实时搜索和过滤功能
- 支持显示/隐藏已删除项目的开关
- 支持批量设置activeRule字段值
- 支持JSON数据导出到剪贴板
- **智能路由匹配**：首次加载时自动定位到与当前页面路由匹配的模块
- **上下文感知**：根据当前页面智能高亮相关菜单项
- **赛博朋克UI设计**：霓虹发光效果、矩阵动画、故障艺术
- **紧致布局**：最小化空间占用，最大化信息密度
- **精致细节**：动态效果、微交互、状态指示器
- 确保脚本在目标网站上正常运行
- 提供沉浸式的赛博朋克操作体验

### 🎯 智能路由匹配逻辑

#### 1. **首次加载优化流程**
```javascript
// 智能定位算法
const SmartRouteDetector = {
    init() {
        const currentPath = window.location.pathname;
        const matchedItems = this.findMatchingMenuItems(currentPath);
        this.highlightAndExpand(matchedItems);
    },
    
    findMatchingMenuItems(currentPath) {
        // 按优先级匹配路由字段
        const matchFields = ['routingUrl', 'activeRule', 'activeRule2', 'url', 'path'];
        return menuData.filter(item => {
            return matchFields.some(field => 
                this.isRouteMatch(currentPath, item[field])
            );
        });
    },
    
    isRouteMatch(currentPath, routePattern) {
        if (!routePattern) return false;
        
        // 支持通配符匹配
        if (routePattern.includes('*')) {
            const regex = new RegExp(
                routePattern.replace(/\*/g, '.*').replace(/\//g, '\\/')
            );
            return regex.test(currentPath);
        }
        
        // 精确匹配或前缀匹配
        return currentPath === routePattern || 
               currentPath.startsWith(routePattern + '/');
    }
};
```

#### 2. **匹配优先级策略**
| 匹配类型 | 优先级 | 描述 | 示例 |
|---------|--------|------|------|
| **精确匹配** | 🔥🔥🔥 | 路径完全一致 | `/admin/user` === `/admin/user` |
| **前缀匹配** | 🔥🔥 | 当前路径以菜单路径开头 | `/admin/user/list` 匹配 `/admin/user` |
| **通配符匹配** | 🔥 | 支持`*`通配符 | `/admin/user/list` 匹配 `/admin/user/*` |
| **模糊匹配** | ⭐ | 包含关键路径段 | `/system/user` 匹配 `user` |

#### 3. **智能展示行为**
```javascript
// 首次加载时的智能行为
const SmartDisplay = {
    onFirstLoad(matchedItems) {
        if (matchedItems.length > 0) {
            // 1. 展开匹配项的父模块
            this.expandParentModules(matchedItems);
            
            // 2. 高亮匹配的菜单项
            this.highlightMatchedItems(matchedItems);
            
            // 3. 滚动到第一个匹配项
            this.scrollToFirstMatch(matchedItems[0]);
            
            // 4. 显示匹配提示
            this.showMatchNotification(matchedItems.length);
        } else {
            // 无匹配时显示最常用的模块
            this.showDefaultModule();
        }
    },
    
    highlightMatchedItems(items) {
        items.forEach(item => {
            // 添加特殊的"当前页面"标识
            item.element.classList.add('current-page-match');
            // 添加脉冲动画效果
            item.element.style.animation = 'cyberpunk-pulse 2s ease-in-out 3';
        });
    }
};
```

#### 4. **视觉反馈增强**
```css
/* 当前页面匹配项的特殊样式 */
.current-page-match {
    border-left: 4px solid #FFD700 !important;
    background: linear-gradient(90deg, 
        rgba(255, 215, 0, 0.1) 0%, 
        transparent 100%);
    position: relative;
}

.current-page-match::before {
    content: "📍";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    animation: glow-pulse 2s infinite;
}

@keyframes cyberpunk-pulse {
    0%, 100% { box-shadow: 0 0 5px #FFD700; }
    50% { box-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700; }
}
```

#### 5. **上下文信息展示**
```
╔═══════════════════════════════════════════════════════════════╗
║ ▲ CYBER MENU MATRIX v2.0.77    📍CONTEXT:/admin/user [↻][RST][✕] ║  
╠═══════════════════════════════════════════════════════════════╣
║ 🔍 [░░░░░░░░░░░░SEARCH░░░░░░░░░░░░] [SCAN] │ 📊[3] ◤MATCHED◢ ║
```

**上下文信息栏说明**：
- `📍CONTEXT:/admin/user` - 显示当前页面路径
- `📊[3] ◤MATCHED◢` - 显示匹配到的菜单项数量

#### 6. **智能Fallback策略**
```javascript
// 无匹配时的智能降级策略
const FallbackStrategy = {
    noMatchFound(currentPath) {
        // 策略1：尝试部分路径匹配
        const pathSegments = currentPath.split('/').filter(Boolean);
        for (let i = pathSegments.length; i > 0; i--) {
            const partialPath = '/' + pathSegments.slice(0, i).join('/');
            const matches = this.findPartialMatches(partialPath);
            if (matches.length > 0) {
                this.showPartialMatches(matches, partialPath);
                return;
            }
        }
        
        // 策略2：显示最常用模块
        this.showMostUsedModule();
        
        // 策略3：显示第一个可用模块
        this.showFirstAvailableModule();
    },
    
    showMostUsedModule() {
        // 根据commonModule字段或访问频率显示
        const commonModules = menuData.filter(item => 
            item.commonModule === '1' || item.sortNo <= 10
        );
        if (commonModules.length > 0) {
            this.expandAndHighlight(commonModules[0]);
        }
    }
};
```

#### 7. **边缘情况处理**
| 场景 | 处理策略 | 用户反馈 |
|------|----------|----------|
| **无任何匹配** | 显示最常用模块 | `🔍 未找到匹配项，显示常用模块` |
| **多个精确匹配** | 按sortNo排序，显示第一个 | `📍 找到${count}个匹配项` |
| **路径为根路径`/`** | 显示首页相关模块 | `🏠 检测到首页，显示主要模块` |
| **动态路由参数** | 忽略参数部分匹配 | `🎯 忽略参数，匹配基础路径` |
| **Hash路由** | 支持#号后的路径匹配 | `#️⃣ 检测到Hash路由` |

#### 8. **性能优化考虑**
```javascript
// 缓存匹配结果，避免重复计算
const RouteMatchCache = {
    cache: new Map(),
    
    getMatch(currentPath) {
        if (this.cache.has(currentPath)) {
            return this.cache.get(currentPath);
        }
        
        const result = SmartRouteDetector.findMatchingMenuItems(currentPath);
        this.cache.set(currentPath, result);
        
        // 限制缓存大小
        if (this.cache.size > 50) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        return result;
    }
};
```

请创建完整的Tampermonkey脚本文件，包含所有必要的功能实现。

---

## 🚀 脚本设计附加建议

### 💡 功能增强建议

#### 1. 数据管理优化
- **版本控制**：实现菜单配置的版本管理，支持回滚到历史版本
- **变更日志**：记录每次修改的时间、用户、变更内容
- **导入/导出**：支持JSON配置文件的导入导出功能
- **配置模板**：预设常用的菜单配置模板

#### 2. 用户体验增强
- **快捷键支持**：
  - `Ctrl+S` 保存修改
  - `Ctrl+Z` 撤销操作
  - `Ctrl+Y` 重做操作
  - `F2` 重命名选中项
- **拖拽排序**：支持通过拖拽调整菜单项的排序
- **批量操作**：支持批量修改多个字段（不仅限于activeRule）
- **预览模式**：修改前预览效果，避免误操作

#### 3. 安全性建议
- **操作确认**：关键操作需要二次确认
- **权限校验**：确保只有管理员能执行敏感操作
- **数据备份**：自动备份原始配置数据
- **操作审计**：记录所有修改操作的审计日志

#### 4. 性能优化
- **虚拟滚动**：处理大量菜单项时使用虚拟滚动
- **懒加载**：按需加载菜单子项
- **缓存机制**：缓存常用的菜单配置
- **防抖搜索**：搜索功能添加防抖机制

### 🔧 技术实现建议

#### 1. 代码架构
```javascript
// 建议的模块化结构
const CyberMenuMatrix = {
    core: {
        dataManager: {},    // 数据管理
        apiService: {},     // API服务
        eventHandler: {},   // 事件处理
    },
    ui: {
        renderer: {},       // 界面渲染
        animator: {},       // 动画效果
        themeManager: {},   // 主题管理
    },
    utils: {
        storage: {},        // 本地存储
        validator: {},      // 数据验证
        formatter: {},      // 格式化工具
    }
};
```

#### 2. 动画效果实现
- **CSS变量**：使用CSS自定义属性管理颜色和动画
- **Web Animations API**：实现流畅的赛博朋克动效
- **SVG图标**：使用矢量图标确保清晰度
- **性能优化**：使用`transform`和`opacity`进行动画

#### 3. 响应式设计
- **断点设计**：
  - Desktop: `1200px+`
  - Tablet: `768px-1199px`
  - Mobile: `<768px`
- **弹性布局**：使用Flexbox和Grid实现自适应
- **触摸友好**：移动端优化触摸交互

### 🛡️ 兼容性建议

#### 1. 浏览器支持
- **Chrome 88+**：主要测试环境
- **Firefox 85+**：次要支持
- **Safari 14+**：基础支持
- **Edge 88+**：基础支持

#### 2. 脚本管理器
- **Tampermonkey**：主要支持
- **Greasemonkey**：基础支持
- **Violentmonkey**：测试验证

### 📊 监控和调试

#### 1. 错误处理
```javascript
// 建议的错误处理机制
try {
    // 核心逻辑
} catch (error) {
    console.error('[CyberMenuMatrix]', error);
    showNotification('操作失败，请检查控制台', 'error');
    // 发送错误报告（可选）
}
```

#### 2. 调试工具
- **开发模式**：添加详细的日志输出
- **性能监控**：监控渲染和操作性能
- **内存管理**：避免内存泄漏

### 🎨 视觉设计细节

#### 1. 动画时序
- **进入动画**：300ms ease-out
- **退出动画**：200ms ease-in
- **悬停效果**：150ms ease-in-out
- **选择反馈**：100ms ease-out

#### 2. 字体建议
```css
font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
```

#### 3. 层级管理
- **模态框**：`z-index: 10000+`
- **悬浮按钮**：`z-index: 9999`
- **提示信息**：`z-index: 9998`

### 🔮 未来扩展

#### 1. 插件系统
- 支持自定义插件扩展功能
- 提供插件API接口
- 插件市场（社区贡献）

#### 2. 云端同步
- 配置云端备份
- 多设备同步
- 团队协作功能

#### 3. AI集成
- 智能配置建议
- 异常检测和修复建议
- 自动化配置优化

这些建议将使你的赛博朋克菜单管理器更加完善、专业和实用！ 🚀✨

---

## 🏗️ 推荐技术栈：现代融合方案

### 📦 **方案选择：Preact + Tailwind + TypeScript**

基于需求分析，推荐使用现代技术栈融合方案，兼得快速开发和专业架构优势：

```javascript
// 融合技术栈配置
{
    "runtime": "Tampermonkey",
    "框架": "Preact 10.x (3KB gzipped)",
    "样式": "Tailwind CSS + CSS-in-JS混合模式", 
    "动画": "Framer Motion + GSAP精选功能",
    "图标": "Lucide Preact (树摇优化)",
    "状态": "Zustand (2KB)",
    "类型": "TypeScript",
    "构建": "Vite + Rollup + PostCSS",
    "开发": "热重载 + 实时预览"
}
```

### 🏗️ **项目架构设计**

```typescript
// 项目结构
src/
├── main.tsx                        // 入口文件
├── App.tsx                         // 主应用组件
├── components/                     // UI组件
│   ├── CyberPanel.tsx             // 主面板组件
│   ├── ModuleTree.tsx             // 模块树组件
│   ├── MenuList.tsx               // 菜单列表组件
│   ├── SearchBar.tsx              // 搜索栏组件
│   └── ui/                        // 基础UI组件
│       ├── Button.tsx             // 按钮组件
│       ├── Input.tsx              // 输入框组件
│       └── Modal.tsx              // 模态框组件
├── hooks/                         // 自定义Hooks
│   ├── useMenuData.ts             // 菜单数据管理
│   ├── useSmartRouter.ts          // 智能路由匹配
│   └── useLocalStorage.ts         // 本地存储
├── stores/                        // Zustand状态管理
│   ├── menuStore.ts               // 菜单状态
│   ├── uiStore.ts                 // UI状态
│   └── settingsStore.ts           // 设置状态
├── utils/                         // 工具函数
│   ├── api.ts                     // API服务
│   ├── router.ts                  // 路由匹配
│   └── animations.ts              // 动画工具
├── styles/                        // 样式文件
│   ├── globals.css                // 全局样式
│   ├── cyberpunk.css              // 赛博朋克主题
│   └── components.css             // 组件样式
└── types/                         // TypeScript类型
    ├── menu.ts                    // 菜单类型定义
    └── api.ts                     // API类型定义
```

### 🎨 **样式方案：Tailwind + CSS-in-JS混合**

```typescript
// 1. Tailwind CSS用于基础样式和布局
const MenuCard = ({ item, isSelected }: MenuCardProps) => {
  return (
    <div className={cn(
      // Tailwind基础样式
      "relative p-4 rounded-lg border transition-all duration-300",
      "bg-gray-900 border-cyan-500/30 hover:border-cyan-400",
      "cursor-pointer select-none",
      // 条件样式
      isSelected && "border-pink-500 bg-pink-500/10",
      item.deleteFlag && "opacity-50 grayscale"
    )}>
      {/* 内容 */}
    </div>
  );
};

// 2. CSS-in-JS用于复杂动画和主题
const CyberGlow = styled.div<{ glowColor: string }>`
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    inset: -1px;
    background: linear-gradient(45deg, 
      ${props => props.glowColor}, 
      transparent, 
      ${props => props.glowColor}
    );
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
    animation: cyber-pulse 2s infinite;
  }
  
  @keyframes cyber-pulse {
    0%, 100% { filter: blur(1px); }
    50% { filter: blur(3px); }
  }
`;
```

### 🔧 **状态管理：增强版Zustand**

```typescript
// stores/menuStore.ts
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

interface MenuStore {
  // 数据状态
  originalData: MenuItem[];
  modifiedData: MenuItem[];
  selectedItems: string[];
  
  // UI状态
  searchQuery: string;
  showDeleted: boolean;
  currentPath: string;
  
  // 动作
  loadMenuData: () => Promise<void>;
  updateMenuItem: (id: string, updates: Partial<MenuItem>) => void;
  batchUpdateActiveRule: (value: string) => void;
  toggleSelection: (id: string) => void;
  
  // 智能路由匹配
  findMatchingItems: (path: string) => MenuItem[];
  highlightCurrentPage: () => void;
}

export const useMenuStore = create<MenuStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      originalData: [],
      modifiedData: [],
      selectedItems: [],
      searchQuery: '',
      showDeleted: false,
      currentPath: '',

      // 实现各种动作
      loadMenuData: async () => {
        const data = await fetchMenuData();
        set((state) => {
          state.originalData = data;
          state.modifiedData = data;
        });
      },

      updateMenuItem: (id, updates) => {
        set((state) => {
          const item = state.modifiedData.find(item => item.id === id);
          if (item) {
            Object.assign(item, updates);
          }
        });
      },

      batchUpdateActiveRule: (value) => {
        const { selectedItems } = get();
        set((state) => {
          selectedItems.forEach(id => {
            const item = state.modifiedData.find(item => item.id === id);
            if (item) {
              item.activeRule = value;
            }
          });
        });
      },

      // 智能路由匹配
      findMatchingItems: (path) => {
        const { modifiedData } = get();
        return modifiedData.filter(item => 
          SmartRouteDetector.isRouteMatch(path, item.routingUrl) ||
          SmartRouteDetector.isRouteMatch(path, item.activeRule)
        );
      }
    }))
  )
);
```

---

## 🔧 油猴脚本打包完整方案

### 📦 **构建配置：Vite + Rollup**

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import preact from '@preact/preset-vite';
import { resolve } from 'path';
import { readFileSync } from 'fs';

export default defineConfig({
  plugins: [
    preact(),
    // 自定义插件：添加油猴头部
    {
      name: 'tampermonkey-header',
      generateBundle(options, bundle) {
        // 读取头部模板
        const header = readFileSync('./scripts/tampermonkey-header.js', 'utf8');
        
        // 为每个输出文件添加头部
        Object.keys(bundle).forEach(fileName => {
          const file = bundle[fileName];
          if (file.type === 'chunk') {
            file.code = header + '\n' + file.code;
          }
        });
      }
    }
  ],
  
  define: {
    // 开发环境变量
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '2.0.77'),
  },
  
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
        // CSS内联插件
        require('postcss-inline-base64')({
          baseDir: './src/assets'
        }),
      ],
    },
  },
  
  build: {
    // 输出单个文件
    lib: {
      entry: resolve(__dirname, 'src/main.tsx'),
      name: 'CyberMenuMatrix',
      fileName: () => 'menu-batch-editor.user.js',
      formats: ['iife'],
    },
    
    rollupOptions: {
      output: {
        // 确保所有代码都内联到单个文件中
        inlineDynamicImports: true,
        manualChunks: undefined,
        
        // 自定义IIFE包装
        format: 'iife',
        name: 'CyberMenuMatrix',
        
        // 添加严格模式和自执行
        intro: '"use strict";',
        outro: 'CyberMenuMatrix.init();',
      },
      
      // 不打包external库（如果有的话）
      external: [],
    },
    
    // 生产环境优化
    minify: process.env.NODE_ENV === 'production' ? 'terser' : false,
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info'],
      },
      mangle: {
        // 保留某些函数名（如果需要的话）
        reserved: ['CyberMenuMatrix'],
      },
    },
    
    // 输出目录
    outDir: 'dist',
    emptyOutDir: true,
  },
  
  // 开发服务器
  server: {
    port: 3000,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
});
```

### 📝 **油猴头部模板**

```javascript
// scripts/tampermonkey-header.js
// ==UserScript==
// @name         Cyber Menu Matrix - 菜单权限批量编辑器
// @namespace    https://github.com/your-username/cyber-menu-matrix
// @version      2.0.77
// @description  赛博朋克风格的菜单权限批量管理工具，支持智能路由匹配和批量编辑
// <AUTHOR>
// @match        https://your-domain.com/*
// @match        http://localhost:*/*
// @icon         data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2IDJMMTAgNVYxOEwxNiAyMUwyMiAxOFY1TDE2IDJaIiBmaWxsPSIjMDBGRkZGIiBvcGFjaXR5PSIwLjgiLz4KPHN2Zz4K
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_listValues
// @grant        GM_setClipboard
// @grant        GM_notification
// @grant        unsafeWindow
// @run-at       document-end
// @updateURL    https://raw.githubusercontent.com/your-username/cyber-menu-matrix/main/dist/menu-batch-editor.user.js
// @downloadURL  https://raw.githubusercontent.com/your-username/cyber-menu-matrix/main/dist/menu-batch-editor.user.js
// @supportURL   https://github.com/your-username/cyber-menu-matrix/issues
// @homepageURL  https://github.com/your-username/cyber-menu-matrix
// ==/UserScript==

/* jshint esversion: 2022 */
/* globals GM_setValue, GM_getValue, GM_deleteValue, GM_listValues, GM_setClipboard, GM_notification, unsafeWindow */

/**
 * Cyber Menu Matrix v2.0.77
 * 赛博朋克风格菜单权限批量编辑器
 * 
 * 特性：
 * - 🎨 赛博朋克UI设计
 * - 🎯 智能路由匹配
 * - ⚡ 批量编辑功能
 * - 💾 数据持久化
 * - 🔍 实时搜索过滤
 * 
 * <AUTHOR>
 * @license MIT
 */

(function() {
```

### 🎯 **主入口文件**

```typescript
// src/main.tsx
import { render } from 'preact';
import { App } from './App';
import './styles/globals.css';

// 全局类型声明
declare global {
  interface Window {
    CyberMenuMatrix: {
      init: () => void;
      destroy: () => void;
      version: string;
    };
  }
  
  // Tampermonkey API
  const GM_setValue: (key: string, value: any) => void;
  const GM_getValue: (key: string, defaultValue?: any) => any;
  const GM_setClipboard: (text: string) => void;
  const GM_notification: (options: any) => void;
}

class CyberMenuMatrix {
  private container: HTMLElement | null = null;
  private isInitialized = false;

  init = () => {
    if (this.isInitialized) return;
    
    console.log('[Cyber Menu Matrix] Initializing v2.0.77...');
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', this.bootstrap);
    } else {
      this.bootstrap();
    }
  };

  private bootstrap = () => {
    try {
      this.createContainer();
      this.renderApp();
      this.isInitialized = true;
      console.log('[Cyber Menu Matrix] ✅ Initialized successfully');
    } catch (error) {
      console.error('[Cyber Menu Matrix] ❌ Initialization failed:', error);
    }
  };

  private createContainer = () => {
    // 检查是否已存在
    const existing = document.getElementById('cyber-menu-matrix');
    if (existing) {
      existing.remove();
    }

    // 创建容器
    this.container = document.createElement('div');
    this.container.id = 'cyber-menu-matrix';
    this.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      pointer-events: none;
    `;
    
    document.body.appendChild(this.container);
  };

  private renderApp = () => {
    if (!this.container) return;
    
    render(<App />, this.container);
  };

  destroy = () => {
    if (this.container) {
      this.container.remove();
      this.container = null;
    }
    this.isInitialized = false;
    console.log('[Cyber Menu Matrix] 🗑️ Destroyed');
  };

  get version() {
    return '2.0.77';
  }
}

// 导出到全局
const instance = new CyberMenuMatrix();
window.CyberMenuMatrix = instance;

// 自动初始化（构建时会在outro中调用）
// instance.init(); // 这行会被vite配置中的outro替换
```

### 🚀 **构建脚本**

```json
// package.json
{
  "name": "cyber-menu-matrix",
  "version": "2.0.77",
  "scripts": {
    "dev": "vite",
    "build": "npm run build:prod",
    "build:dev": "NODE_ENV=development vite build && npm run post-build",
    "build:prod": "NODE_ENV=production vite build && npm run post-build",
    "post-build": "node scripts/post-build.js",
    "preview": "vite preview",
    "type-check": "tsc --noEmit",
    "lint": "eslint src --ext .ts,.tsx"
  },
  "dependencies": {
    "preact": "^10.19.0",
    "zustand": "^4.4.0", 
    "clsx": "^2.0.0",
    "lucide-preact": "^0.300.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "vite": "^5.0.0",
    "@preact/preset-vite": "^2.5.0",
    "tailwindcss": "^3.3.0",
    "framer-motion": "^10.16.0",
    "gsap": "^3.12.0"
  }
}
```

```javascript
// scripts/post-build.js
const fs = require('fs');
const path = require('path');

async function postBuild() {
  const distPath = path.resolve(__dirname, '../dist');
  const files = fs.readdirSync(distPath);
  
  // 找到生成的JS文件
  const jsFile = files.find(file => file.endsWith('.user.js'));
  if (!jsFile) {
    console.error('❌ No .user.js file found!');
    return;
  }
  
  const filePath = path.join(distPath, jsFile);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 确保文件以闭包结束
  if (!content.includes('})();')) {
    content += '\n})();';
  }
  
  // 添加错误处理包装
  content = content.replace(
    /^(\/\/ ==UserScript==[\s\S]*?\/\/ ==\/UserScript==\n)/,
    `$1
(function() {
  'use strict';
  
  try {
`
  );
  
  content += `
  } catch (error) {
    console.error('[Cyber Menu Matrix] Fatal error:', error);
    if (typeof GM_notification !== 'undefined') {
      GM_notification({
        title: 'Cyber Menu Matrix Error',
        text: 'Script failed to initialize. Check console for details.',
        timeout: 5000
      });
    }
  }
})();`;
  
  // 写回文件
  fs.writeFileSync(filePath, content);
  
  // 显示构建信息
  const stats = fs.statSync(filePath);
  const sizeKB = (stats.size / 1024).toFixed(2);
  
  console.log(`✅ Build completed!`);
  console.log(`📦 File: ${jsFile}`);
  console.log(`📏 Size: ${sizeKB} KB`);
  console.log(`🔗 Path: ${filePath}`);
}

postBuild().catch(console.error);
```

### 🔄 **开发工作流**

```bash
# 1. 初始化项目
npm create vite@latest cyber-menu-matrix --template preact-ts
cd cyber-menu-matrix
npm install

# 2. 安装依赖
npm install zustand clsx lucide-preact
npm install -D tailwindcss autoprefixer postcss

# 3. 开发模式 - 热重载开发
npm run dev

# 4. 构建开发版本 - 保留console.log
npm run build:dev

# 5. 构建生产版本 - 压缩优化
npm run build:prod

# 6. 类型检查
npm run type-check

# 7. 代码检查
npm run lint
```

### 📋 **最终输出**

```
dist/
└── menu-batch-editor.user.js    // 最终的油猴脚本文件

文件特点：
✅ 单文件包含所有代码和样式
✅ 包含完整的油猴头部信息  
✅ 压缩优化（生产版约45-60KB）
✅ 错误处理和日志记录
✅ 自动初始化逻辑
✅ 现代开发体验 + 油猴兼容性
```

### 🎯 **使用方法**

1. **开发阶段**：
   ```bash
   npm run dev
   # 在 http://localhost:3000 开发和调试
   ```

2. **构建阶段**：
   ```bash
   npm run build:prod
   # 生成 dist/menu-batch-editor.user.js
   ```

3. **安装到油猴**：
   - 复制 `dist/menu-batch-editor.user.js` 的内容
   - 在Tampermonkey中创建新脚本
   - 粘贴代码并保存

4. **自动更新**：
   - 将脚本上传到GitHub
   - 用户会自动收到更新通知

### 🔧 **高级配置**

```typescript
// 环境变量配置
// .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_DEBUG_MODE=true

// .env.production  
VITE_API_BASE_URL=https://your-api.com
VITE_DEBUG_MODE=false
```

### 📊 **方案优势总结**

✅ **开发效率** - Preact + Tailwind 快速开发  
✅ **类型安全** - TypeScript 全覆盖  
✅ **动画丰富** - Framer Motion + GSAP 专业动效  
✅ **组件化** - 可复用的现代架构  
✅ **性能优秀** - 构建后仍然保持轻量 (45-60KB)  
✅ **维护性** - 模块化设计，易于扩展  
✅ **油猴兼容** - 完美适配Tampermonkey环境  

这套方案既享受现代开发体验，又能输出高质量的油猴脚本！ 🚀✨