// ==UserScript==
// @name         菜单权限管理器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  管理系统菜单权限，支持修改activeRule
// <AUTHOR>
// @match        *://*************:*/*
// @match        *://**************:*/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  let originalMenuData = null;
  let currentMenuData = null;
  let isModalOpen = false;
  let selectedItems = new Set();
  let isSelecting = false;
  let selectionStartY = 0;
  let showDeletedItems = false;
  let isCtrlPressed = false;
  let hasTriedFetchMenu = false; // 标记是否已经尝试过获取菜单
  let showCurrentTreeOnly = false; // 是否只显示当前树
  let currentTreeRootId = null; // 当前树的根节点ID
  let isAllSelected = false; // 是否全选状态

  // 搜索相关变量
  let searchMatches = [];
  let currentMatchIndex = -1;
  let searchQuery = '';

  // 本地存储键名
  const STORAGE_KEYS = {
    ACTIVE_RULE_INPUT: 'menu_manager_active_rule_input',
    SELECTED_ITEMS: 'menu_manager_selected_items',
    MENU_DATA: 'menu_manager_menu_data',
    SHOW_DELETED: 'menu_manager_show_deleted'
  };

  // 本地存储工具函数
  function saveToStorage(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
      console.warn('保存到本地存储失败:', e);
    }
  }

  function loadFromStorage(key, defaultValue) {
    if (defaultValue === undefined) {
      defaultValue = null;
    }
    try {
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : defaultValue;
    } catch (e) {
      console.warn('从本地存储读取失败:', e);
      return defaultValue;
    }
  }

  // 获取IP地址
  async function getIPAddress() {
    try {
      // 尝试多个IP获取服务
      const services = [
        'https://api.ipify.org?format=json',
        'https://httpbin.org/ip',
        'https://api.ip.sb/ip'
      ];

      for (const service of services) {
        try {
          const response = await fetch(service);
          const data = await response.json();

          // 根据不同服务的响应格式提取IP
          if (data.ip) return data.ip;
          if (data.origin) return data.origin;
          if (typeof data === 'string') return data.trim();
        } catch (e) {
          console.warn(`IP服务 ${service} 失败:`, e);
          continue;
        }
      }

      // 如果所有外部服务都失败，尝试本地方法
      return await getLocalIP();
    } catch (e) {
      console.error('获取IP地址失败:', e);
      throw new Error('无法获取IP地址');
    }
  }

  // 获取本地IP（WebRTC方法）
  function getLocalIP() {
    return new Promise((resolve, reject) => {
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      pc.createDataChannel('');
      pc.createOffer().then(offer => pc.setLocalDescription(offer));

      pc.onicecandidate = (event) => {
        if (event.candidate) {
          const candidate = event.candidate.candidate;
          const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
          if (ipMatch) {
            pc.close();
            resolve(ipMatch[1]);
          }
        }
      };

      setTimeout(() => {
        pc.close();
        reject(new Error('获取本地IP超时'));
      }, 3000);
    });
  }

  // 检查是否为管理员
  function isAdminUser() {
    try {
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        console.log('localStorage中未找到user信息');
        return false;
      }

      const user = JSON.parse(userStr);
      const { id, roleList } = user;

      console.log('用户信息:', { id, roleList });

      // 判断是否为管理员：id为'1'或roleList中包含code为'administrator'的角色
      const isAdmin = id === '1' || (roleList && roleList.map((el) => el.code).includes('administrator'));
      
      console.log('管理员判断结果:', isAdmin);
      return isAdmin;
    } catch (e) {
      console.warn('解析用户信息失败:', e);
      return false;
    }
  }

  // 主动获取菜单接口
  async function fetchMenuData() {
    const baseUrl = window.location.origin;
    const isAdmin = isAdminUser();

         // 根据用户类型确定菜单接口优先级
     const menuApiUrls = isAdmin ? [
       // 管理员优先调用privilegeTree接口
       `${baseUrl}/dxdsapi/PrivilegeProduct/privilegeTree/1?projectCode=1`,
       `${baseUrl}/dxdsapi/PrivilegeProduct/myPrivilege/1`,
     ] : [
       // 普通用户优先调用myPrivilege接口
       `${baseUrl}/dxdsapi/PrivilegeProduct/myPrivilege/1`,
       `${baseUrl}/dxdsapi/PrivilegeProduct/privilegeTree/1?projectCode=1`,
     ];

     console.log(`检测到${isAdmin ? '管理员' : '普通'}用户，优先使用${isAdmin ? 'privilegeTree' : 'myPrivilege'}接口`);

    for (const menuApiUrl of menuApiUrls) {
      try {
        console.log('正在尝试获取菜单数据:', menuApiUrl);

        const response = await fetch(menuApiUrl, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          console.warn(`接口 ${menuApiUrl} 返回错误: HTTP ${response.status}`);
          continue;
        }

        const data = await response.json();

        if (data && data.data) {
          originalMenuData = JSON.parse(JSON.stringify(data));
          currentMenuData = JSON.parse(JSON.stringify(data));

          // 保存到本地存储
          saveToStorage(STORAGE_KEYS.MENU_DATA, currentMenuData);

          console.log('菜单数据获取成功:', {
            url: menuApiUrl,
            dataCount: data.data.length,
            data: data,
            userType: isAdmin ? '管理员' : '普通用户'
          });

          return true;
        } else {
          console.warn(`接口 ${menuApiUrl} 返回数据格式不正确:`, data);
          continue;
        }
      } catch (e) {
        console.warn(`接口 ${menuApiUrl} 请求失败:`, e);
        continue;
      }
    }

    // 如果所有接口都失败了
    throw new Error('所有菜单接口都无法获取数据');
  }

  // 初始化时加载本地存储的数据
  function initializeFromStorage() {
    // 加载显示删除项目状态（默认为true，显示已删除项目）
    showDeletedItems = loadFromStorage(STORAGE_KEYS.SHOW_DELETED, true);

    // 加载选中项目
    const savedSelectedItems = loadFromStorage(STORAGE_KEYS.SELECTED_ITEMS, []);
    selectedItems = new Set(savedSelectedItems);

    // 加载菜单数据
    const savedMenuData = loadFromStorage(STORAGE_KEYS.MENU_DATA, null);
    if (savedMenuData) {
      originalMenuData = JSON.parse(JSON.stringify(savedMenuData));
      currentMenuData = JSON.parse(JSON.stringify(savedMenuData));
    }
  }

  // 赛博朋克消息提示系统
  function showCyberpunkMessage(message, type = 'info', duration = 3000) {
    // 创建消息元素
    const toast = document.createElement('div');
    toast.className = `cyberpunk-toast ${type}`;
    toast.textContent = message;

    // 添加到页面
    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
      toast.classList.add('closing');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, duration);

    return toast;
  }

  // 消息提示的便捷方法
  function showSuccess(message, duration = 3000) {
    return showCyberpunkMessage(message, 'success', duration);
  }

  function showError(message, duration = 4000) {
    return showCyberpunkMessage(message, 'error', duration);
  }

  function showWarning(message, duration = 3500) {
    return showCyberpunkMessage(message, 'warning', duration);
  }

  function showInfo(message, duration = 3000) {
    return showCyberpunkMessage(message, 'info', duration);
  }

  // 检查是否为目标接口
  function isTargetAPI(url) {
    if (typeof url !== 'string') return false;

    // 检查是否包含菜单权限相关的API路径
    const privilegeProductPaths = [
      '/dxdsapi/PrivilegeProduct/myPrivilege',
      '/dxdsapi/PrivilegeProduct/privilegeTree/1',
    ];

    const isPrivilegeAPI = privilegeProductPaths.some(path => url.includes(path));

    if (!isPrivilegeAPI) return false;

    // 使用正则表达式匹配更灵活的参数组合
    let urlObj;
    try {
      urlObj = new URL(url, window.location.origin);
    } catch (e) {
      // 如果URL解析失败，尝试简单的字符串匹配
      console.warn('URL解析失败，使用字符串匹配:', url);
      return isPrivilegeAPI;
    }

    const params = urlObj.searchParams;

    // 检查关键参数
    const hasPrivilegeParentId = params.has('privilegeParentId');
    const hasProjectCode = params.has('projectCode');

    // 额外的模式匹配
    const targetPatterns = [
      // 具体的参数组合
      /privilegeParentId=\d+.*projectCode=\d+/,
      /projectCode=\d+.*privilegeParentId=\d+/,
      // 单独的参数
      /privilegeParentId=\d+/,
      /projectCode=\d+/,
      // 菜单相关的其他可能参数
      /parentId=\d+/,
      /menuId=\d+/,
      /resourceId=\d+/,
      // privilegeTree 特有的路径参数
      /privilegeTree\/\d+/,
      // 其他可能的路径参数
      /\/\d+\?projectCode=\d+/
    ];

    const hasTargetPattern = targetPatterns.some(pattern => pattern.test(url));

    // 如果是权限API路径，就拦截（无论是否有参数）
    return isPrivilegeAPI && (hasTargetPattern || hasPrivilegeParentId || hasProjectCode || !urlObj.search);
  }

  // 拦截API请求
  const originalFetch = window.fetch;
  window.fetch = function (...args) {
    const url = args[0];
    if (isTargetAPI(url)) {
      console.log('拦截到菜单权限接口:', url);
      return originalFetch.apply(this, args).then(response => {
        const clonedResponse = response.clone();
        clonedResponse.json().then(data => {
          if (data && data.data) {
            originalMenuData = JSON.parse(JSON.stringify(data));
            currentMenuData = JSON.parse(JSON.stringify(data));
            console.log('菜单权限数据已捕获 (fetch):', {
              url: url,
              dataCount: data.data.length,
              data: data
            });
          }
        }).catch(err => {
          console.warn('解析菜单数据失败 (fetch):', err);
        });
        return response;
      });
    }
    return originalFetch.apply(this, args);
  };

  // 拦截XMLHttpRequest
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function (method, url, ...args) {
    this._url = url;
    this._method = method;
    return originalXHROpen.apply(this, [method, url, ...args]);
  };

  XMLHttpRequest.prototype.send = function (...args) {
    if (this._url && isTargetAPI(this._url)) {
      console.log('拦截到菜单权限接口 (XHR):', this._method, this._url);
      this.addEventListener('load', function () {
        try {
          const data = JSON.parse(this.responseText);
          if (data && data.data) {
            originalMenuData = JSON.parse(JSON.stringify(data));
            currentMenuData = JSON.parse(JSON.stringify(data));
            console.log('菜单权限数据已捕获 (XHR):', {
              method: this._method,
              url: this._url,
              dataCount: data.data.length,
              data: data
            });
          }
        } catch (e) {
          console.error('解析菜单数据失败 (XHR):', e, {
            url: this._url,
            response: this.responseText
          });
        }
      });
    }
    return originalXHRSend.apply(this, args);
  };

  // 创建样式
  const style = document.createElement('style');
  style.textContent = `
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        .menu-manager-float-btn {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%) !important;
            border: 2px solid #00d4ff !important;
            border-radius: 50% !important;
            cursor: move !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: #00d4ff !important;
            font-size: 24px !important;
            box-shadow: 
                0 0 20px rgba(0, 212, 255, 0.5),
                0 0 40px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1) !important;
            transition: all 0.3s ease !important;
            user-select: none !important;
            font-family: 'Orbitron', monospace !important;
            animation: cyberpunk-pulse 2s infinite !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
        }
        
        @keyframes cyberpunk-pulse {
            0%, 100% { 
                box-shadow: 
                    0 0 20px rgba(0, 212, 255, 0.5),
                    0 0 40px rgba(0, 212, 255, 0.3),
                    inset 0 0 20px rgba(0, 212, 255, 0.1);
            }
            50% { 
                box-shadow: 
                    0 0 30px rgba(0, 212, 255, 0.8),
                    0 0 60px rgba(0, 212, 255, 0.5),
                    inset 0 0 30px rgba(0, 212, 255, 0.2);
            }
        }
        
        .menu-manager-float-btn svg {
            width: 24px !important;
            height: 24px !important;
            fill: currentColor !important;
            transition: all 0.3s ease !important;
        }
        
        .menu-manager-float-btn:hover {
            transform: scale(1.1) !important;
            color: #ffffff !important;
            border-color: #ff0080 !important;
            box-shadow: 
                0 0 30px rgba(255, 0, 128, 0.8),
                0 0 60px rgba(255, 0, 128, 0.5),
                inset 0 0 30px rgba(255, 0, 128, 0.2) !important;
        }
        
        .menu-manager-float-btn:hover svg {
            transform: rotate(90deg) !important;
        }
        
        .menu-manager-modal {
            position: fixed;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            backdrop-filter: blur(10px);
            animation: modal-fade-in 0.3s ease-out;
        }
        
        @keyframes modal-fade-in {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        @keyframes slide-in-right {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slide-out-right {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .menu-manager-modal-content {
            background: 
                linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            width: 85%;
            max-width: 1400px;
            height: 100%;
            border-radius: 0;
            border-left: 3px solid #00d4ff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 
                -20px 0 50px rgba(0, 212, 255, 0.3),
                -40px 0 100px rgba(0, 212, 255, 0.2),
                inset 0 0 50px rgba(0, 212, 255, 0.1);
            font-family: 'Orbitron', monospace;
            animation: slide-in-right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .menu-manager-modal-content.closing {
            animation: slide-out-right 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        }
        
        .menu-manager-header-bar {
            width: 100%;
            background: 
                linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(255, 0, 128, 0.2) 100%);
            padding: 20px;
            border-bottom: 2px solid #00d4ff;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .menu-manager-title {
            color: #00d4ff;
            font-size: 24px;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 3px;
            text-shadow: 
                0 0 10px rgba(0, 212, 255, 0.8),
                0 0 20px rgba(0, 212, 255, 0.5),
                0 0 30px rgba(0, 212, 255, 0.3);
            margin: 0;
            animation: title-glow 3s ease-in-out infinite alternate;
        }
        
        @keyframes title-glow {
            from {
                text-shadow: 
                    0 0 10px rgba(0, 212, 255, 0.8),
                    0 0 20px rgba(0, 212, 255, 0.5),
                    0 0 30px rgba(0, 212, 255, 0.3);
            }
            to {
                text-shadow: 
                    0 0 15px rgba(0, 212, 255, 1),
                    0 0 25px rgba(0, 212, 255, 0.8),
                    0 0 35px rgba(0, 212, 255, 0.6);
            }
        }
        
        .menu-manager-main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .menu-manager-left-panel {
            width: 35%;
            background: 
                linear-gradient(180deg, rgba(10, 10, 10, 0.8) 0%, rgba(26, 26, 46, 0.9) 100%);
            padding: 20px;
            overflow-y: auto;
            border-right: 2px solid #00d4ff;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .menu-manager-left-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 212, 255, 0.03) 2px,
                    rgba(0, 212, 255, 0.03) 4px
                );
            pointer-events: none;
        }
        
        .menu-manager-left-panel h3 {
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
            margin-top: 0;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .menu-manager-right-panel {
            width: 65%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            background: 
                linear-gradient(180deg, rgba(22, 33, 62, 0.8) 0%, rgba(10, 10, 10, 0.9) 100%);
            overflow-y: auto;
        }
        
        .menu-manager-header {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #00d4ff;
            position: relative;
        }
        
        .menu-manager-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00d4ff, #ff0080, #00d4ff, transparent);
            animation: cyberpunk-line 4s ease-in-out infinite;
        }
        
        @keyframes cyberpunk-line {
            0% { 
                left: 0%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% { 
                left: 70%;
                opacity: 0;
            }
        }
        
        .menu-manager-input {
            flex: 1;
            min-width: 200px;
            padding: 12px 16px;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            font-size: 14px;
            background: rgba(10, 10, 10, 0.8);
            color: #00d4ff;
            font-family: 'Orbitron', monospace;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .menu-manager-input:focus {
            outline: none;
            border-color: #ff0080;
            box-shadow: 
                0 0 20px rgba(255, 0, 128, 0.5),
                inset 0 0 20px rgba(255, 0, 128, 0.1);
            color: #ffffff;
        }
        
        .menu-manager-input::placeholder {
            color: rgba(0, 212, 255, 0.6);
        }
        
        .menu-manager-btn {
            padding: 12px 20px;
            border: 2px solid;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .menu-manager-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .menu-manager-btn:hover::before {
            left: 100%;
        }
        
        .menu-manager-btn-primary {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            border-color: #00d4ff;
        }
        
        .menu-manager-btn-primary:hover {
            background: rgba(0, 212, 255, 0.3);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            color: #ffffff;
        }
        
        .menu-manager-btn-secondary {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
            border-color: #6c757d;
        }
        
        .menu-manager-btn-secondary:hover {
            background: rgba(108, 117, 125, 0.3);
            box-shadow: 0 0 20px rgba(108, 117, 125, 0.5);
            color: #ffffff;
        }
        
        .menu-manager-btn-success {
            background: rgba(255, 0, 128, 0.1);
            color: #ff0080;
            border-color: #ff0080;
        }
        
        .menu-manager-btn-success:hover {
            background: rgba(255, 0, 128, 0.3);
            box-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
            color: #ffffff;
        }
        
        .menu-manager-json-container {
            position: relative;
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-top: 15px;
            min-height: 300px;
        }
        
        .menu-manager-copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
        }
        
        .menu-manager-json-editor {
            flex: 1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            padding: 15px;
            resize: none;
            background: rgba(10, 10, 10, 0.9);
            color: #00d4ff;
            line-height: 1.5;
            transition: all 0.3s ease;
        }
        
        .menu-manager-json-editor:focus {
            outline: none;
            border-color: #ff0080;
            box-shadow: 
                0 0 20px rgba(255, 0, 128, 0.3),
                inset 0 0 20px rgba(255, 0, 128, 0.1);
        }
        
        .menu-item {
            padding: 15px;
            margin: 8px 0;
            background: 
                linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(10, 10, 10, 0.8) 100%);
            border-radius: 10px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        
        /* Ctrl键按下时的视觉提示 */
        body.ctrl-pressed .menu-item {
            cursor: copy;
        }
        
        body.ctrl-pressed .menu-item::after {
            content: 'Ctrl+点击切换';
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 0, 128, 0.8);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            pointer-events: none;
            z-index: 10;
        }
        
        body.ctrl-pressed .menu-item:hover::after {
            opacity: 1;
            transform: translateY(0);
        }
        
        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .menu-item:hover {
            background: 
                linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(26, 26, 46, 0.9) 100%);
            border-color: #00d4ff;
            box-shadow: 
                0 0 20px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
            transform: translateX(5px);
        }
        
        .menu-item:hover::before {
            left: 100%;
        }
        
        .menu-item.selected {
            background: 
                linear-gradient(135deg, rgba(255, 0, 128, 0.3) 0%, rgba(0, 212, 255, 0.2) 100%);
            color: #ffffff;
            border-color: #ff0080;
            box-shadow: 
                0 0 30px rgba(255, 0, 128, 0.5),
                inset 0 0 30px rgba(255, 0, 128, 0.2);
            transform: translateX(10px);
        }
        
        .menu-item.multi-selected {
            background: 
                linear-gradient(135deg, rgba(0, 255, 128, 0.3) 0%, rgba(0, 212, 255, 0.2) 100%);
            color: #ffffff;
            border-color: #00ff80;
            box-shadow: 
                0 0 20px rgba(0, 255, 128, 0.5),
                inset 0 0 20px rgba(0, 255, 128, 0.2);
            transform: translateX(8px);
        }
        
        .menu-item.deleted {
            opacity: 0.7;
            background: 
                linear-gradient(135deg, rgba(128, 128, 128, 0.1) 0%, rgba(64, 64, 64, 0.8) 100%);
            border-color: rgba(128, 128, 128, 0.3);
            cursor: pointer;
        }
        
        .menu-item.deleted:hover {
            background: 
                linear-gradient(135deg, rgba(128, 128, 128, 0.2) 0%, rgba(64, 64, 64, 0.9) 100%);
            border-color: rgba(128, 128, 128, 0.5);
            box-shadow: 
                0 0 15px rgba(128, 128, 128, 0.3),
                inset 0 0 15px rgba(128, 128, 128, 0.1);
            transform: translateX(3px);
        }
        
        .menu-item.deleted.multi-selected {
            background: 
                linear-gradient(135deg, rgba(255, 165, 0, 0.3) 0%, rgba(128, 128, 128, 0.2) 100%);
            color: #ffffff;
            border-color: #ffa500;
            box-shadow: 
                0 0 20px rgba(255, 165, 0, 0.5),
                inset 0 0 20px rgba(255, 165, 0, 0.2);
            transform: translateX(8px);
        }
        
        .menu-level-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .menu-level-container .menu-item {
            flex: 0 0 calc(50% - 5px);
            margin: 0;
        }
        
        .menu-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(128, 128, 128, 0.3);
            transition: 0.3s;
            border-radius: 24px;
            border: 1px solid rgba(0, 212, 255, 0.5);
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background: #00d4ff;
            transition: 0.3s;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .toggle-switch input:checked + .toggle-slider {
            background: rgba(0, 212, 255, 0.3);
        }
        
        .toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(26px);
            background: #ff0080;
            box-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
        }
        
        .control-label {
            color: #00d4ff;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 12px;
        }
        
        .interaction-badge {
            display: inline-block;
            padding: 2px 8px;
            background: rgba(255, 0, 128, 0.2);
            border: 1px solid #ff0080;
            border-radius: 12px;
            color: #ff0080;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: 8px;
        }
        
        .menu-item-name {
            font-weight: 700;
            margin-bottom: 8px;
            color: #00d4ff;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
            font-family: 'Orbitron', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .menu-item.selected .menu-item-name {
            color: #ffffff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }
        
        .menu-item-info {
            font-size: 11px;
            opacity: 0.8;
            color: rgba(0, 212, 255, 0.8);
            font-family: 'Courier New', monospace;
            line-height: 1.4;
        }
        
        .menu-item.selected .menu-item-info {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .close-btn {
            background: 
                linear-gradient(135deg, rgba(255, 0, 128, 0.8) 0%, rgba(220, 53, 69, 0.8) 100%);
            color: white;
            border: 2px solid #ff0080;
            border-radius: 8px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .close-btn::before {
            content: '⨯';
            position: relative;
            z-index: 1;
        }
        
        .close-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }
        
        .close-btn:hover {
            background: 
                linear-gradient(135deg, rgba(255, 0, 128, 1) 0%, rgba(220, 53, 69, 1) 100%);
            box-shadow: 
                0 0 20px rgba(255, 0, 128, 0.8),
                0 0 40px rgba(255, 0, 128, 0.5);
            transform: scale(1.05);
        }
        
        .close-btn:hover::after {
            left: 100%;
        }
        
        /* 滚动条样式 */
        .menu-manager-left-panel::-webkit-scrollbar {
            width: 8px;
        }
        
        .menu-manager-left-panel::-webkit-scrollbar-track {
            background: rgba(10, 10, 10, 0.5);
            border-radius: 4px;
        }
        
        .menu-manager-left-panel::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #00d4ff, #ff0080);
            border-radius: 4px;
        }
        
        .menu-manager-left-panel::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #ff0080, #00d4ff);
        }
        
        /* 搜索容器样式 */
        .menu-search-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }
        
        .menu-search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            background: rgba(10, 10, 10, 0.8);
            color: #00d4ff;
            font-family: 'Orbitron', monospace;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .menu-search-input:focus {
            outline: none;
            border-color: #ff0080;
            box-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
            color: #ffffff;
        }
        
        .menu-search-input::placeholder {
            color: rgba(0, 212, 255, 0.6);
        }
        
        .search-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-nav-btn {
            width: 28px;
            height: 28px;
            border: 1px solid rgba(0, 212, 255, 0.5);
            border-radius: 4px;
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-size: 14px;
            font-weight: 700;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-nav-btn:hover {
            background: rgba(0, 212, 255, 0.3);
            border-color: #00d4ff;
            box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }
        
        .search-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: rgba(128, 128, 128, 0.1);
            border-color: rgba(128, 128, 128, 0.3);
            color: rgba(128, 128, 128, 0.6);
        }
        
        .search-counter {
            color: #00d4ff;
            font-family: 'Orbitron', monospace;
            font-size: 11px;
            font-weight: 700;
            min-width: 40px;
            text-align: center;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
        }
        
        /* 搜索高亮样式 */
        .menu-item.search-highlight {
            background: 
                linear-gradient(135deg, rgba(255, 255, 0, 0.3) 0%, rgba(255, 165, 0, 0.2) 100%);
            border-color: #ffff00;
            box-shadow: 
                0 0 20px rgba(255, 255, 0, 0.5),
                inset 0 0 20px rgba(255, 255, 0, 0.1);
            animation: search-pulse 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes search-pulse {
            from {
                box-shadow: 
                    0 0 20px rgba(255, 255, 0, 0.5),
                    inset 0 0 20px rgba(255, 255, 0, 0.1);
            }
            to {
                box-shadow: 
                    0 0 30px rgba(255, 255, 0, 0.8),
                    inset 0 0 30px rgba(255, 255, 0, 0.2);
            }
        }
        
        /* 自动定位高亮样式 */
        .menu-item.auto-locate-highlight {
            background: 
                linear-gradient(135deg, rgba(0, 255, 128, 0.4) 0%, rgba(0, 212, 255, 0.3) 100%);
            border-color: #00ff80;
            box-shadow: 
                0 0 25px rgba(0, 255, 128, 0.6),
                inset 0 0 25px rgba(0, 255, 128, 0.2);
            animation: auto-locate-pulse 2s ease-in-out infinite alternate;
            transform: translateX(15px);
        }
        
        @keyframes auto-locate-pulse {
            from {
                box-shadow: 
                    0 0 25px rgba(0, 255, 128, 0.6),
                    inset 0 0 25px rgba(0, 255, 128, 0.2);
                transform: translateX(15px) scale(1);
            }
            to {
                box-shadow: 
                    0 0 35px rgba(0, 255, 128, 0.9),
                    inset 0 0 35px rgba(0, 255, 128, 0.3);
                transform: translateX(15px) scale(1.02);
            }
        }
        
        /* 控制面板样式 */
        .menu-controls {
            margin-bottom: 15px;
        }
        
        /* 赛博朋克消息提示 */
        .cyberpunk-toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            min-width: 300px;
            max-width: 500px;
            padding: 15px 20px;
            background: 
                linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(10, 10, 10, 0.95) 100%);
            border: 2px solid #00d4ff;
            border-radius: 8px;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            font-size: 14px;
            font-weight: 700;
            z-index: 999999;
            box-shadow: 
                0 0 20px rgba(0, 212, 255, 0.5),
                0 0 40px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
            animation: toast-slide-down 0.3s ease-out;
            backdrop-filter: blur(10px);
        }
        
        .cyberpunk-toast.success {
            border-color: #00ff80;
            box-shadow: 
                0 0 20px rgba(0, 255, 128, 0.5),
                0 0 40px rgba(0, 255, 128, 0.3),
                inset 0 0 20px rgba(0, 255, 128, 0.1);
        }
        
        .cyberpunk-toast.error {
            border-color: #ff0080;
            box-shadow: 
                0 0 20px rgba(255, 0, 128, 0.5),
                0 0 40px rgba(255, 0, 128, 0.3),
                inset 0 0 20px rgba(255, 0, 128, 0.1);
        }
        
        .cyberpunk-toast.warning {
            border-color: #ffaa00;
            box-shadow: 
                0 0 20px rgba(255, 170, 0, 0.5),
                0 0 40px rgba(255, 170, 0, 0.3),
                inset 0 0 20px rgba(255, 170, 0, 0.1);
        }
        
        .cyberpunk-toast::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: toast-shine 2s ease-in-out infinite;
        }
        
        .cyberpunk-toast.closing {
            animation: toast-slide-up 0.3s ease-in;
        }
        
        @keyframes toast-slide-down {
            from {
                transform: translateX(-50%) translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes toast-slide-up {
            from {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
            to {
                transform: translateX(-50%) translateY(-100%);
                opacity: 0;
            }
        }
        
        @keyframes toast-shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        
        /* 详情弹框样式 */
        .detail-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .form-row {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-row-group {
            display: flex;
            gap: 15px;
        }
        
        .form-row.half {
            flex: 1;
        }
        
        .form-label {
            color: #00d4ff;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
        }
        
        .form-input, .form-textarea {
            padding: 10px 12px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            background: rgba(10, 10, 10, 0.8);
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #ff0080;
            box-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
        }
        
        .form-textarea {
            min-height: 60px;
            resize: vertical;
        }
    `;
  document.head.appendChild(style);

  // 创建悬浮按钮
  function createFloatButton() {
    console.log('🚀 开始创建悬浮按钮');
    console.log('📍 document.readyState:', document.readyState);
    console.log('📍 document.body:', document.body);

    const floatBtn = document.createElement('div');
    floatBtn.className = 'menu-manager-float-btn';
    floatBtn.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11.03L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11.03C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
            </svg>
        `;
    floatBtn.title = '菜单权限管理器';

    console.log("🚀 ~ createFloatButton ~ floatBtn:", floatBtn);

    // 添加按钮到页面的函数
    function addButtonToPage() {
      console.log('📍 正在添加按钮到页面');
      console.log('📍 document.body:', document.body);

      if (document.body) {
        document.body.appendChild(floatBtn);
        console.log('✅ 按钮已添加到页面');

        // 验证按钮是否真的在页面上
        setTimeout(() => {
          const btnInPage = document.querySelector('.menu-manager-float-btn');
          console.log('🔍 页面中的按钮:', btnInPage);
          console.log('🔍 按钮样式:', btnInPage ? window.getComputedStyle(btnInPage) : '未找到');
        }, 100);
      } else {
        console.warn('⚠️ document.body 不存在，等待DOM加载');
      }
    }

    // 立即尝试添加
    if (document.readyState === 'loading') {
      console.log('📍 DOM正在加载，等待DOMContentLoaded事件');
      document.addEventListener('DOMContentLoaded', addButtonToPage);
    } else {
      console.log('📍 DOM已加载，立即添加按钮');
      addButtonToPage();
    }

    // 额外的保险措施
    setTimeout(() => {
      if (!document.querySelector('.menu-manager-float-btn')) {
        console.log('🔄 按钮未找到，重新尝试添加');
        addButtonToPage();
      }
    }, 1000);

    return floatBtn;
  }

  const floatBtn = createFloatButton();

  // 拖拽功能
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };

  floatBtn.addEventListener('mousedown', (e) => {
    isDragging = true;
    dragOffset.x = e.clientX - floatBtn.offsetLeft;
    dragOffset.y = e.clientY - floatBtn.offsetTop;
    floatBtn.style.cursor = 'grabbing';
  });

  document.addEventListener('mousemove', (e) => {
    if (isDragging) {
      const x = e.clientX - dragOffset.x;
      const y = e.clientY - dragOffset.y;
      floatBtn.style.left = Math.max(0, Math.min(window.innerWidth - 60, x)) + 'px';
      floatBtn.style.top = Math.max(0, Math.min(window.innerHeight - 60, y)) + 'px';
      floatBtn.style.right = 'auto';
      floatBtn.style.bottom = 'auto';
    }
  });

  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false;
      floatBtn.style.cursor = 'move';
    }
  });

  // 点击事件
  floatBtn.addEventListener('click', (e) => {
    if (!isDragging) {
      openModal();
    }
  });

  // 创建模态框
  function openModal() {
    if (isModalOpen) {
      return;
    }

    // 如果没有菜单数据且还没有尝试过获取，则尝试获取
    if (!currentMenuData && !hasTriedFetchMenu) {
      hasTriedFetchMenu = true;
      fetchMenuData().then(() => {
        if (currentMenuData) {
          openModal(); // 递归调用，但此时hasTriedFetchMenu已经为true
        } else {
          showWarning('无法获取菜单数据，请刷新页面或等待接口调用');
        }
      }).catch(() => {
        showWarning('获取菜单数据失败，请刷新页面或等待接口调用');
      });
      return;
    }

    // 如果没有菜单数据且已经尝试过获取，则显示警告
    if (!currentMenuData) {
      showWarning('暂未捕获到菜单数据，请刷新页面或等待接口调用');
      return;
    }

    isModalOpen = true;
    const modal = document.createElement('div');
    modal.className = 'menu-manager-modal';

    modal.innerHTML = `
            <div class="menu-manager-modal-content">
                <div class="menu-manager-header-bar">
                    <h1 class="menu-manager-title">菜单权限管理器</h1>
                    <button class="close-btn" id="closeBtn"></button>
                </div>
                <div class="menu-manager-main-content">
                    <div class="menu-manager-left-panel">
                        <div class="menu-manager-header">
                            <input type="text" class="menu-manager-input" id="activeRuleInput" placeholder="输入新的activeRule值" style="width: 100%; margin-bottom: 15px;">
                            <div style="display: flex; gap: 10px; flex-wrap: wrap; width: 100%;">
                                <button class="menu-manager-btn menu-manager-btn-success" id="getIpBtn">获取IP</button>
                                <button class="menu-manager-btn menu-manager-btn-primary" id="setBtn">设置</button>
                                <button class="menu-manager-btn menu-manager-btn-secondary" id="resetBtn">重置</button>
                                <button class="menu-manager-btn menu-manager-btn-secondary" id="toggleJsonBtn">隐藏JSON</button>
                            </div>
                        </div>
                        <div class="menu-search-container">
                            <input type="text" class="menu-search-input" id="menuSearchInput" placeholder="搜索菜单 (name, code, activeRule...)">
                            <div class="search-controls">
                                <button class="search-nav-btn" id="prevMatchBtn" title="上一项">↑</button>
                                <span class="search-counter" id="searchCounter">0/0</span>
                                <button class="search-nav-btn" id="nextMatchBtn" title="下一项">↓</button>
                            </div>
                        </div>
                        <div class="menu-controls">
                            <span class="control-label">显示已删除项目</span>
                            <label class="toggle-switch">
                                <input type="checkbox" id="showDeletedToggle">
                                <span class="toggle-slider"></span>
                            </label>
                            <button class="menu-manager-btn menu-manager-btn-primary" id="showCurrentTreeBtn">只显示当前树</button>
                        </div>
                        <div class="menu-controls">
                            <button class="menu-manager-btn menu-manager-btn-success" id="toggleSelectAllBtn">全选菜单</button>
                            <button class="menu-manager-btn menu-manager-btn-primary" id="fetchMenuBtn">获取</button>
                            <button class="menu-manager-btn menu-manager-btn-success" id="autoLocateBtn">定位</button>
                        </div>
                        <div class="menu-manager-json-container" id="jsonContainer" style="display: flex;">
                            <button class="menu-manager-btn menu-manager-btn-success menu-manager-copy-btn" id="copyBtn">复制JSON</button>
                            <textarea class="menu-manager-json-editor" id="jsonEditor" readonly></textarea>
                        </div>
                    </div>
                    <div class="menu-manager-right-panel">
                        <h3 style="margin-top: 0; color: #00d4ff; text-shadow: 0 0 10px rgba(0, 212, 255, 0.8); font-weight: 900; text-transform: uppercase; letter-spacing: 2px;">菜单列表</h3>
                        <div style="margin-bottom: 15px; padding: 10px; background: rgba(0, 212, 255, 0.1); border-radius: 6px; border: 1px solid rgba(0, 212, 255, 0.3);">
                            <div style="color: #00d4ff; font-size: 11px; font-family: 'Orbitron', monospace; line-height: 1.4;">
                                <strong>操作提示：</strong><br>
                                • 单击：选中单个菜单项，自动定位到JSON位置<br>
                                • Ctrl/Cmd+单击：切换菜单项选中状态（多选）<br>
                                • 拖拽：连续选择多个菜单项<br>
                                • Ctrl/Cmd+拖拽：连续切换多个菜单项状态<br>
                                • 双击：查看菜单项详细信息<br>
                                <span style="color: #00ff80; font-size: 10px;">✓ 智能路由匹配：优先匹配 routingUrl 字段</span><br>
                                <span style="color: #ff0080; font-size: 10px;">✓ 自动定位：打开时自动定位到当前页面对应的菜单项</span>
                            </div>
                        </div>
                        <div id="menu-list"></div>
                    </div>
                </div>
            </div>
        `;

    document.body.appendChild(modal);

    // 渲染菜单列表
    renderMenuList();

    // 显示完整JSON
    updateJsonEditor();

    // 自动定位到当前路由对应的菜单项
    autoLocateCurrentRoute();

    // 初始化时更新按钮状态
    setTimeout(() => {
      updateSelectAllButtonState();
    }, 100);

    // 绑定事件
    const closeBtn = modal.querySelector('#closeBtn');
    const copyBtn = modal.querySelector('#copyBtn');
    const setBtn = modal.querySelector('#setBtn');
    const resetBtn = modal.querySelector('#resetBtn');
    const getIpBtn = modal.querySelector('#getIpBtn');
    const fetchMenuBtn = modal.querySelector('#fetchMenuBtn');
    const autoLocateBtn = modal.querySelector('#autoLocateBtn');
    const clearSelectionBtn = modal.querySelector('#clearSelectionBtn');
    const showDeletedToggle = modal.querySelector('#showDeletedToggle');
    const activeRuleInput = modal.querySelector('#activeRuleInput');
    const menuSearchInput = modal.querySelector('#menuSearchInput');
    const prevMatchBtn = modal.querySelector('#prevMatchBtn');
    const nextMatchBtn = modal.querySelector('#nextMatchBtn');
    const searchCounter = modal.querySelector('#searchCounter');
    const toggleJsonBtn = modal.querySelector('#toggleJsonBtn');
    const jsonContainer = modal.querySelector('#jsonContainer');
    const showCurrentTreeBtn = modal.querySelector('#showCurrentTreeBtn');
    const toggleSelectAllBtn = modal.querySelector('#toggleSelectAllBtn');

    // 初始化界面状态
    if (showDeletedToggle && showDeletedToggle instanceof HTMLInputElement) {
      showDeletedToggle.checked = showDeletedItems;
    }

    // 加载保存的输入框值
    if (activeRuleInput && activeRuleInput instanceof HTMLInputElement) {
      const savedInputValue = loadFromStorage(STORAGE_KEYS.ACTIVE_RULE_INPUT, '');
      if (savedInputValue) {
        activeRuleInput.value = savedInputValue;
      }

      // 监听输入框变化并保存
      activeRuleInput.addEventListener('input', () => {
        saveToStorage(STORAGE_KEYS.ACTIVE_RULE_INPUT, activeRuleInput.value);
      });
    }

    if (closeBtn) closeBtn.addEventListener('click', closeModalWithAnimation);
    if (copyBtn) copyBtn.addEventListener('click', copyJson);
    if (setBtn) setBtn.addEventListener('click', setActiveRule);
    if (resetBtn) resetBtn.addEventListener('click', resetData);
    if (getIpBtn) getIpBtn.addEventListener('click', handleGetIP);
    if (fetchMenuBtn) fetchMenuBtn.addEventListener('click', handleFetchMenu);
    if (autoLocateBtn) autoLocateBtn.addEventListener('click', autoLocateCurrentRoute);
    if (clearSelectionBtn) clearSelectionBtn.addEventListener('click', handleClearSelection);
    if (toggleJsonBtn) toggleJsonBtn.addEventListener('click', handleToggleJson);
    if (showCurrentTreeBtn) showCurrentTreeBtn.addEventListener('click', handleShowCurrentTree);
    if (toggleSelectAllBtn) toggleSelectAllBtn.addEventListener('click', handleToggleSelectAll);
    if (showDeletedToggle) {
      showDeletedToggle.addEventListener('change', (e) => {
        const target = e.target;
        if (target && target instanceof HTMLInputElement) {
          showDeletedItems = target.checked;
          saveToStorage(STORAGE_KEYS.SHOW_DELETED, showDeletedItems);
          renderMenuList();
          // 显示/隐藏状态改变后，需要更新UI状态
          setTimeout(() => {
            updateUIStatesAfterDataChange();
          }, 100);
        }
      });
    }

    // 搜索相关事件绑定
    if (menuSearchInput) {
      menuSearchInput.addEventListener('input', handleSearch);
      menuSearchInput.addEventListener('keydown', (e) => {
        if (e instanceof KeyboardEvent && e.key === 'Enter') {
          e.preventDefault();
          if (e.shiftKey) {
            navigateSearchResults('prev');
          } else {
            navigateSearchResults('next');
          }
        }
      });
    }
    if (prevMatchBtn) prevMatchBtn.addEventListener('click', () => navigateSearchResults('prev'));
    if (nextMatchBtn) nextMatchBtn.addEventListener('click', () => navigateSearchResults('next'));
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModalWithAnimation();
    });
  }

  function renderMenuList() {
    const menuList = document.getElementById('menu-list');
    if (!menuList || !currentMenuData || !currentMenuData.data) return;

    menuList.innerHTML = '';

    // 排序函数
    function sortItems(items) {
      return items.sort((a, b) => {
        const sortA = parseInt(a.sortNo) || 0;
        const sortB = parseInt(b.sortNo) || 0;
        return sortA - sortB;
      });
    }

    // 获取交互方式显示文本
    function getDisplayLocationText(displayLocation) {
      const locationMap = {
        '_micro': '微前端',
        '_iframe': 'iframe',
        '_blank': '新窗口',
        '_self': '当前窗口'
      };
      return locationMap[displayLocation] || displayLocation || '未知';
    }

    function renderMenuLevel(items, level = 0) {
      if (!items || items.length === 0) return;

      // 过滤和排序
      let filteredItems = items.filter(item => {
        // 调试信息：打印菜单项信息
        console.log('菜单项过滤检查:', {
          name: item.name,
          code: item.code,
          menu: item.menu,
          deleteFlag: item.deleteFlag
        });

        // 只显示menu为true的项目（如果menu字段存在的话）
        // 如果没有menu字段，则默认显示
        if (item.hasOwnProperty('menu') && !item.menu) {
          console.log('过滤掉：menu不为true', item.name);
          return false;
        }

        // 过滤掉code中包含特定前缀的项目
        if (item.code) {
          const filterPrefixes = ['[button]-', '[link]-', '[tab]-', '[component]-'];
          for (const prefix of filterPrefixes) {
            if (item.code.includes(prefix)) {
              console.log('过滤掉：包含前缀', prefix, item.name);
              return false;
            }
          }
        }

        // 如果不显示删除项目，则过滤掉删除的项目
        if (item.deleteFlag && !showDeletedItems) {
          console.log('过滤掉：已删除项目', item.name);
          return false;
        }

        console.log('保留菜单项:', item.name);
        return true;
      });

      filteredItems = sortItems(filteredItems);

      // 检查是否所有项目都没有子项（需要检查过滤后的子项）
      const hasAnyChildren = filteredItems.some(item => {
        if (!item.children || item.children.length === 0) return false;

        // 对子项应用相同的过滤逻辑
        const filteredChildren = item.children.filter(child => {
          // 只显示menu为true的项目（如果menu字段存在的话）
          if (child.hasOwnProperty('menu') && !child.menu) {
            return false;
          }

          // 过滤掉code中包含特定前缀的项目
          if (child.code) {
            const filterPrefixes = ['[button]-', '[link]-', '[tab]-', '[component]-'];
            for (const prefix of filterPrefixes) {
              if (child.code.includes(prefix)) {
                return false;
              }
            }
          }

          // 如果不显示删除项目，则过滤掉删除的项目
          if (child.deleteFlag && !showDeletedItems) {
            return false;
          }

          return true;
        });

        return filteredChildren.length > 0;
      });

      if (!hasAnyChildren && level > 0) {
        // 横向布局
        const levelContainer = document.createElement('div');
        levelContainer.className = 'menu-level-container';
        levelContainer.style.marginLeft = (level * 30) + 'px';

        filteredItems.forEach(item => {
          const menuItem = createMenuItem(item, level, true);
          levelContainer.appendChild(menuItem);
        });

        menuList.appendChild(levelContainer);
      } else {
        // 纵向布局
        filteredItems.forEach(item => {
          const menuItem = createMenuItem(item, level, false);
          menuList.appendChild(menuItem);

          // 递归渲染子菜单
          if (item.children && item.children.length > 0) {
            renderMenuLevel(item.children, level + 1);
          }
        });
      }
    }

    function createMenuItem(item, level, isHorizontal) {
      const menuItem = document.createElement('div');
      menuItem.className = 'menu-item';
      if (!isHorizontal) {
        menuItem.style.marginLeft = (level * 30) + 'px';
      }
      menuItem.dataset.itemId = item.id;

      // 检查是否已选中
      if (selectedItems.has(item.id)) {
        menuItem.classList.add('multi-selected');
      }

      // 检查是否已删除
      if (item.deleteFlag) {
        menuItem.classList.add('deleted');
      }

      const interactionText = getDisplayLocationText(item.displayLocation);

      menuItem.innerHTML = `
                <div class="menu-item-name">
                    ${item.name || item.code}
                    <span class="interaction-badge">${interactionText}</span>
                </div>
                <div class="menu-item-info">
                    ID: ${item.id} | Code: ${item.code} | Sort: ${item.sortNo || 0}<br>
                    ActiveRule: ${item.activeRule || '无'}<br>
                    ActiveRule2: ${item.activeRule2 || '无'}<br>
                    RoutingUrl: ${item.routingUrl || '无'}
                </div>
            `;

      // 单击选择/取消选择（已删除项目也可以选中）
      menuItem.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (e.ctrlKey || e.metaKey) {
          // Ctrl+点击：切换选中状态
          toggleItemSelection(item.id, menuItem);
        } else {
          // 普通点击：清空其他选择，只选中当前项
          clearAllSelections();
          selectItem(item.id, menuItem);
        }

        updateInputAndJson();
      });

      // 双击事件
      menuItem.addEventListener('dblclick', (e) => {
        e.preventDefault();
        e.stopPropagation();
        showItemDetailModal(item);
      });

      // 鼠标按下事件处理（合并所有逻辑）
      let ctrlDragStarted = false;

      menuItem.addEventListener('mousedown', (e) => {
        if (e.ctrlKey || e.metaKey) {
          // Ctrl+鼠标按下：需要区分点击和拖拽
          const startX = e.clientX;
          const startY = e.clientY;

          const handleMouseMove = (moveEvent) => {
            const deltaX = Math.abs(moveEvent.clientX - startX);
            const deltaY = Math.abs(moveEvent.clientY - startY);

            // 如果鼠标移动超过5像素，认为是拖拽
            if ((deltaX > 5 || deltaY > 5) && !ctrlDragStarted) {
              ctrlDragStarted = true;
              isSelecting = true;
              selectionStartY = startY;

              // 开始拖拽时，先处理当前项目（拖拽起始点）
              toggleItemSelection(item.id, menuItem);
              updateInputAndJson();

              // 移除鼠标移动监听器，避免重复触发
              document.removeEventListener('mousemove', handleMouseMove);
            }
          };

          const handleMouseUp = () => {
            // 清理事件监听器
            ctrlDragStarted = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
          };

          // 临时添加监听器来检测是否是拖拽
          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        } else {
          // 普通鼠标按下：立即启动拖拽选择
          isSelecting = true;
          selectionStartY = e.clientY;

          // 普通拖拽：清空之前的选择并选中当前项
          clearAllSelections();
          selectItem(item.id, menuItem);
          updateInputAndJson();
        }
      });

      // 鼠标进入时的拖拽选择
      menuItem.addEventListener('mouseenter', (e) => {
        if (isSelecting) {
          // 使用全局Ctrl键状态，因为拖拽过程中事件对象可能不准确
          if (isCtrlPressed) {
            // Ctrl+拖拽：切换选择状态
            toggleItemSelection(item.id, menuItem);
          } else {
            // 普通拖拽：直接选中
            selectItem(item.id, menuItem);
          }
          updateInputAndJson();
        }
      });

      return menuItem;
    }

    if (currentMenuData && currentMenuData.data) {
      let dataToRender = currentMenuData.data;
      
      // 如果启用了只显示当前树模式，过滤数据
      if (showCurrentTreeOnly && currentTreeRootId) {
        const rootItem = currentMenuData.data.find(item => item.id === currentTreeRootId);
        if (rootItem) {
          dataToRender = [rootItem];
        }
      }
      
      renderMenuLevel(dataToRender, 0);

      // 重新渲染后更新UI状态
      setTimeout(() => {
        updateUIStatesAfterDataChange();
      }, 100);
    }

    // 添加全局事件监听
    document.addEventListener('mouseup', () => {
      isSelecting = false;
    });

    // 监听键盘事件以跟踪Ctrl键状态
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        isCtrlPressed = true;
        document.body.classList.add('ctrl-pressed');
      }
    });

    document.addEventListener('keyup', (e) => {
      if (!e.ctrlKey && !e.metaKey) {
        isCtrlPressed = false;
        document.body.classList.remove('ctrl-pressed');
      }
    });

    // 监听窗口失焦事件，确保Ctrl状态正确重置
    window.addEventListener('blur', () => {
      isCtrlPressed = false;
      document.body.classList.remove('ctrl-pressed');
    });
  }

  // 统一的按钮状态更新函数
  function updateSelectAllButtonState() {
    const toggleSelectAllBtn = document.getElementById('toggleSelectAllBtn');
    if (!toggleSelectAllBtn) return;

    // 获取当前可见的菜单项总数
    const visibleMenuItems = document.querySelectorAll('.menu-item[data-item-id]:not([style*="display: none"])');
    const visibleItemIds = Array.from(visibleMenuItems).map(el => el.getAttribute('data-item-id')).filter(id => id);
    
    // 检查是否全部可见项都已选中
    const allVisibleSelected = visibleItemIds.length > 0 && visibleItemIds.every(id => selectedItems.has(id));
    
    if (allVisibleSelected && visibleItemIds.length > 0) {
      // 全选状态
      isAllSelected = true;
      toggleSelectAllBtn.textContent = '取消全选';
      toggleSelectAllBtn.classList.remove('menu-manager-btn-success');
      toggleSelectAllBtn.classList.add('menu-manager-btn-secondary');
    } else {
      // 非全选状态
      isAllSelected = false;
      toggleSelectAllBtn.textContent = '全选菜单';
      toggleSelectAllBtn.classList.remove('menu-manager-btn-secondary');
      toggleSelectAllBtn.classList.add('menu-manager-btn-success');
    }
  }

  // 更新搜索和显示状态
  function updateUIStatesAfterDataChange() {
    // 清除无效的选中项（已被过滤掉的项目）
    const validItemIds = new Set();
    document.querySelectorAll('.menu-item[data-item-id]').forEach(el => {
      const itemId = el.getAttribute('data-item-id');
      if (itemId) validItemIds.add(itemId);
    });
    
    // 清除不再可见的选中项
    const itemsToRemove = Array.from(selectedItems).filter(itemId => !validItemIds.has(itemId));
    
    itemsToRemove.forEach(itemId => selectedItems.delete(itemId));
    
    // 保存更新后的选中项
    saveSelectedItems();
    
    // 更新按钮状态
    updateSelectAllButtonState();
    
    // 重新应用搜索高亮（如果有搜索）
    if (searchQuery) {
      setTimeout(() => {
        performSearch();
      }, 100);
    }
    
    // 更新输入框和JSON
    updateInputAndJson();
  }

  function selectItem(itemId, menuElement) {
    selectedItems.add(itemId);
    menuElement.classList.add('multi-selected');
    menuElement.classList.remove('selected');
    saveSelectedItems();
    updateSelectAllButtonState();
  }

  function toggleItemSelection(itemId, menuElement) {
    if (selectedItems.has(itemId)) {
      selectedItems.delete(itemId);
      menuElement.classList.remove('multi-selected');
    } else {
      selectedItems.add(itemId);
      menuElement.classList.add('multi-selected');
    }
    menuElement.classList.remove('selected');
    saveSelectedItems();
    updateSelectAllButtonState();
  }

  function clearAllSelections() {
    selectedItems.clear();
    document.querySelectorAll('.menu-item').forEach(el => {
      el.classList.remove('selected', 'multi-selected');
    });
    saveSelectedItems();
    updateSelectAllButtonState();
  }

  function saveSelectedItems() {
    saveToStorage(STORAGE_KEYS.SELECTED_ITEMS, Array.from(selectedItems));
  }

  function updateInputAndJson() {
    const input = document.getElementById('activeRuleInput');
    const jsonEditor = document.getElementById('jsonEditor');

    if (selectedItems.size === 1) {
      // 单选时显示该项的activeRule
      const selectedId = Array.from(selectedItems)[0];
      const selectedItem = findItemById(selectedId);
      if (input && input instanceof HTMLInputElement && selectedItem) {
        input.value = selectedItem.activeRule || '';
      }
      
      // 定位到JSON中对应的位置
      if (selectedItem) {
        locateItemInJson(selectedItem);
      }
    } else if (selectedItems.size > 1) {
      // 多选时清空输入框
      if (input && input instanceof HTMLInputElement) {
        input.value = '';
        input.placeholder = `已选择 ${selectedItems.size} 个菜单项`;
      }
    }

    // 始终显示完整的菜单JSON
    if (jsonEditor && jsonEditor instanceof HTMLTextAreaElement) {
      jsonEditor.value = JSON.stringify(currentMenuData, null, 2);
    }
  }

  function findItemById(itemId) {
    function searchInItems(items) {
      for (let item of items) {
        if (item.id === itemId) {
          return item;
        }
        if (item.children) {
          const found = searchInItems(item.children);
          if (found) return found;
        }
      }
      return null;
    }

    if (currentMenuData && currentMenuData.data) {
      return searchInItems(currentMenuData.data);
    }
    return null;
  }

  function updateJsonEditor() {
    const jsonEditor = document.getElementById('jsonEditor');
    if (jsonEditor && jsonEditor instanceof HTMLTextAreaElement) {
      jsonEditor.value = JSON.stringify(currentMenuData, null, 2);
    }
  }

  // 在JSON中定位到指定菜单项
  function locateItemInJson(targetItem) {
    const jsonEditor = document.getElementById('jsonEditor');
    const jsonContainer = document.getElementById('jsonContainer');
    
    if (!jsonEditor || !(jsonEditor instanceof HTMLTextAreaElement) || !targetItem) {
      return;
    }

    // 确保JSON面板是可见的
    if (jsonContainer && jsonContainer.style.display === 'none') {
      jsonContainer.style.display = 'flex';
      const toggleJsonBtn = document.getElementById('toggleJsonBtn');
      if (toggleJsonBtn) {
        toggleJsonBtn.textContent = '隐藏JSON';
      }
    }

    const jsonText = jsonEditor.value;
    if (!jsonText) return;

    // 查找目标项的唯一标识符，优先使用ID（最准确）
    const searchPatterns = [];
    
    // ID是最准确的标识符
    if (targetItem.id) {
      searchPatterns.push(`"id": ${targetItem.id}`);
      searchPatterns.push(`"id":${targetItem.id}`);
    }
    
    // 如果有code，也添加到搜索模式中
    if (targetItem.code) {
      searchPatterns.push(`"code": "${targetItem.code}"`);
      searchPatterns.push(`"code":"${targetItem.code}"`);
    }
    
    // 名称可能重复，优先级最低
    if (targetItem.name) {
      searchPatterns.push(`"name": "${targetItem.name}"`);
      searchPatterns.push(`"name":"${targetItem.name}"`);
    }

    let foundIndex = -1;
    let foundPattern = '';

    // 尝试不同的搜索模式
    for (const pattern of searchPatterns) {
      const index = jsonText.indexOf(pattern);
      if (index !== -1) {
        foundIndex = index;
        foundPattern = pattern;
        break;
      }
    }

    if (foundIndex === -1) {
      console.warn('未在JSON中找到对应的菜单项:', targetItem);
      return;
    }

    // 计算光标位置（行号和列号）
    const beforeText = jsonText.substring(0, foundIndex);
    const lines = beforeText.split('\n');
    const lineNumber = lines.length;
    const columnNumber = lines[lines.length - 1].length;

    console.log('在JSON中找到菜单项:', {
      item: targetItem.name || targetItem.code,
      pattern: foundPattern,
      position: foundIndex,
      line: lineNumber,
      column: columnNumber
    });

    // 设置光标位置并选中找到的文本
    try {
      jsonEditor.focus();
      jsonEditor.setSelectionRange(foundIndex, foundIndex + foundPattern.length);
      
      // 滚动到可见区域
      const textBeforeCursor = jsonText.substring(0, foundIndex);
      const linesBeforeCursor = textBeforeCursor.split('\n').length - 1;
      const lineHeight = 20; // 估算的行高
      const scrollTop = Math.max(0, (linesBeforeCursor - 5) * lineHeight);
      jsonEditor.scrollTop = scrollTop;

      // 添加高亮效果
      setTimeout(() => {
        jsonEditor.setSelectionRange(foundIndex, foundIndex + foundPattern.length);
      }, 100);

      // showSuccess(`已定位到JSON中的菜单项: ${targetItem.name || targetItem.code}`);
    } catch (e) {
      console.warn('设置JSON光标位置失败:', e);
    }
  }

  function copyJson() {
    const jsonEditor = document.getElementById('jsonEditor');
    if (jsonEditor && jsonEditor instanceof HTMLTextAreaElement) {
      jsonEditor.select();
      document.execCommand('copy');

      const copyBtn = document.getElementById('copyBtn');
      if (copyBtn) {
        const originalText = copyBtn.textContent;
        copyBtn.textContent = '已复制!';
        setTimeout(() => {
          if (copyBtn) {
            copyBtn.textContent = originalText;
          }
        }, 1000);
      }
    }
  }

  function setActiveRule() {
    const input = document.getElementById('activeRuleInput');

    if (!input || !(input instanceof HTMLInputElement)) {
      showError('输入框不可用');
      return;
    }

    if (selectedItems.size === 0) {
      showWarning('请先选择菜单项');
      return;
    }

    const newValue = input.value.trim();

    // 找到对应的数据项并更新
    function updateMenuItem(items, targetId) {
      for (let item of items) {
        if (item.id === targetId) {
          item.activeRule = newValue;
          item.activeRule2 = newValue; // 同时设置activeRule2
          return true;
        }
        if (item.children && updateMenuItem(item.children, targetId)) {
          return true;
        }
      }
      return false;
    }

    // 更新所有选中的菜单项
    if (currentMenuData && currentMenuData.data) {
      let updatedCount = 0;
      selectedItems.forEach(itemId => {
        if (updateMenuItem(currentMenuData.data, itemId)) {
          updatedCount++;
        }
      });

      // 保存到本地存储
      saveToStorage(STORAGE_KEYS.MENU_DATA, currentMenuData);

      // 重新渲染菜单列表以显示更新后的值
      renderMenuList();

      // 更新JSON编辑器
      updateJsonEditor();

      // 确保选择状态保持一致
      setTimeout(() => {
        updateSelectAllButtonState();
      }, 100);

      // 显示成功消息
      if (selectedItems.size === 1) {
        showSuccess('ActiveRule 和 ActiveRule2 已更新! ');
      } else {
        showSuccess(`已更新 ${updatedCount} 个菜单项的 ActiveRule 和 ActiveRule2! `);
      }
    }
  }

  function resetData() {
    if (originalMenuData) {
      currentMenuData = JSON.parse(JSON.stringify(originalMenuData));
      clearAllSelections();
      renderMenuList();
      updateJsonEditor();
      
      // 重置输入框
      const resetInput = document.getElementById('activeRuleInput');
      if (resetInput && resetInput instanceof HTMLInputElement) {
        resetInput.value = '';
        resetInput.placeholder = '输入新的activeRule值';
      }
      
      // 清除搜索状态
      const searchInput = document.getElementById('menuSearchInput');
      if (searchInput && searchInput instanceof HTMLInputElement) {
        searchInput.value = '';
        searchQuery = '';
        clearSearchHighlights();
        updateSearchCounter();
      }
      
      // 重置树显示模式
      showCurrentTreeOnly = false;
      currentTreeRootId = null;
      const showCurrentTreeBtn = document.getElementById('showCurrentTreeBtn');
      if (showCurrentTreeBtn) {
        showCurrentTreeBtn.textContent = '只显示当前树';
        showCurrentTreeBtn.classList.remove('menu-manager-btn-success');
        showCurrentTreeBtn.classList.add('menu-manager-btn-primary');
      }
      
      // 保存重置后的数据到本地存储
      saveToStorage(STORAGE_KEYS.MENU_DATA, currentMenuData);
      showSuccess('数据已重置! 所有状态已恢复到初始值');
    }
  }

  function closeModalWithAnimation() {
    const modal = document.querySelector('.menu-manager-modal');
    const modalContent = document.querySelector('.menu-manager-modal-content');

    if (modal && modalContent) {
      modalContent.classList.add('closing');
      setTimeout(() => {
        modal.remove();
        isModalOpen = false;
      }, 300);
    }
  }

  function closeModal() {
    const modal = document.querySelector('.menu-manager-modal');
    if (modal) {
      modal.remove();
      isModalOpen = false;
    }
  }

  // 处理获取IP按钮点击
  async function handleGetIP() {
    const getIpBtn = document.getElementById('getIpBtn');
    const activeRuleInput = document.getElementById('activeRuleInput');

    if (!activeRuleInput || !(activeRuleInput instanceof HTMLInputElement)) {
      showError('输入框不可用');
      return;
    }

    if (getIpBtn) {
      const originalText = getIpBtn.textContent;
      getIpBtn.textContent = '获取中...';
      if (getIpBtn instanceof HTMLButtonElement) {
        getIpBtn.disabled = true;
      }

      try {
        const ip = await getIPAddress();
        activeRuleInput.value = ip;
        saveToStorage(STORAGE_KEYS.ACTIVE_RULE_INPUT, ip);
        showSuccess(`IP地址获取成功: ${ip}`);
      } catch (e) {
        showError(`获取IP地址失败: ${e.message}`);
      } finally {
        if (getIpBtn) {
          getIpBtn.textContent = originalText;
          if (getIpBtn instanceof HTMLButtonElement) {
            getIpBtn.disabled = false;
          }
        }
      }
    }
  }

  // 处理获取菜单接口按钮点击
  async function handleFetchMenu() {
    const fetchMenuBtn = document.getElementById('fetchMenuBtn');

    if (fetchMenuBtn) {
      const originalText = fetchMenuBtn.textContent;
      fetchMenuBtn.textContent = '获取中...';
      if (fetchMenuBtn.disabled !== undefined) {
        fetchMenuBtn.disabled = true;
      }

      try {
        hasTriedFetchMenu = true; // 标记已尝试获取
        await fetchMenuData();
        
        // 清空之前的选择状态，因为菜单数据已更新
        clearAllSelections();
        
        renderMenuList();
        updateJsonEditor();
        
        // 尝试自动定位当前路由
        setTimeout(() => {
          autoLocateCurrentRoute();
        }, 200);
        
        showSuccess('菜单数据获取成功!');
      } catch (e) {
        showError(`获取菜单数据失败: ${e.message}`);
      } finally {
        if (fetchMenuBtn) {
          fetchMenuBtn.textContent = originalText;
          if (fetchMenuBtn.disabled !== undefined) {
            fetchMenuBtn.disabled = false;
          }
        }
      }
    }
  }

  // 处理清空选中按钮点击
  function handleClearSelection() {
    clearAllSelections();
    saveToStorage(STORAGE_KEYS.SELECTED_ITEMS, []);
    updateInputAndJson();

    const activeRuleInput = document.getElementById('activeRuleInput');
    if (activeRuleInput && activeRuleInput instanceof HTMLInputElement) {
      activeRuleInput.placeholder = '输入新的activeRule值';
    }

    showInfo('已清空所有选中项目');
  }

  // 处理切换JSON显示/隐藏
  function handleToggleJson() {
    const toggleJsonBtn = document.getElementById('toggleJsonBtn');
    const jsonContainer = document.getElementById('jsonContainer');
    
    if (!toggleJsonBtn || !jsonContainer) return;
    
    const isVisible = jsonContainer.style.display === 'flex';
    
    if (isVisible) {
      // 隐藏JSON
      jsonContainer.style.display = 'none';
      toggleJsonBtn.textContent = '显示JSON';
      showInfo('JSON面板已隐藏');
    } else {
      // 显示JSON
      jsonContainer.style.display = 'flex';
      toggleJsonBtn.textContent = '隐藏JSON';
      // 更新JSON内容
      updateJsonEditor();
      showInfo('JSON面板已显示');
    }
  }

  // 处理只显示当前树
  function handleShowCurrentTree() {
    const showCurrentTreeBtn = document.getElementById('showCurrentTreeBtn');
    if (!showCurrentTreeBtn) return;

    if (!showCurrentTreeOnly) {
      // 启用只显示当前树模式
      // 首先尝试自动定位当前路由对应的菜单项
      const currentPath = window.location.pathname;
      const currentHash = window.location.hash;
      const currentSearch = window.location.search;
      
      if (currentMenuData && currentMenuData.data) {
        const matchedItem = findMatchingMenuItem(currentMenuData.data, currentPath, currentHash, currentSearch);
        
        if (matchedItem) {
          // 找到匹配项，获取其根节点
          currentTreeRootId = findRootNodeId(matchedItem.id);
          showCurrentTreeOnly = true;
          showCurrentTreeBtn.textContent = '显示全部树';
          showCurrentTreeBtn.classList.remove('menu-manager-btn-primary');
          showCurrentTreeBtn.classList.add('menu-manager-btn-success');
          renderMenuList();
          setTimeout(() => {
            updateUIStatesAfterDataChange();
          }, 100);
          showSuccess(`已切换到当前树模式，显示根节点: ${getRootNodeName(currentTreeRootId)}`);
        } else if (selectedItems.size > 0) {
          // 如果没有找到匹配项但有选中项，使用第一个选中项
          const firstSelectedId = Array.from(selectedItems)[0];
          currentTreeRootId = findRootNodeId(firstSelectedId);
          showCurrentTreeOnly = true;
          showCurrentTreeBtn.textContent = '显示全部树';
          showCurrentTreeBtn.classList.remove('menu-manager-btn-primary');
          showCurrentTreeBtn.classList.add('menu-manager-btn-success');
          renderMenuList();
          setTimeout(() => {
            updateUIStatesAfterDataChange();
          }, 100);
          showSuccess(`已切换到当前树模式，显示根节点: ${getRootNodeName(currentTreeRootId)}`);
        } else {
          showWarning('未找到当前路由对应的菜单项，请先选择一个菜单项后再试');
        }
      }
    } else {
      // 关闭只显示当前树模式
      showCurrentTreeOnly = false;
      currentTreeRootId = null;
      showCurrentTreeBtn.textContent = '只显示当前树';
      showCurrentTreeBtn.classList.remove('menu-manager-btn-success');
      showCurrentTreeBtn.classList.add('menu-manager-btn-primary');
      renderMenuList();
      setTimeout(() => {
        updateUIStatesAfterDataChange();
      }, 100);
      showInfo('已切换到显示全部树模式');
    }
  }

  // 处理全选/取消全选
  function handleToggleSelectAll() {
    const toggleSelectAllBtn = document.getElementById('toggleSelectAllBtn');
    if (!toggleSelectAllBtn) return;

    if (!isAllSelected) {
      // 全选所有可见菜单项
      selectAllVisibleMenus();
      isAllSelected = true;
      toggleSelectAllBtn.textContent = '取消全选';
      toggleSelectAllBtn.classList.remove('menu-manager-btn-success');
      toggleSelectAllBtn.classList.add('menu-manager-btn-secondary');
      showSuccess(`已全选 ${selectedItems.size} 个菜单项`);
    } else {
      // 取消全选
      clearAllSelections();
      isAllSelected = false;
      toggleSelectAllBtn.textContent = '全选菜单';
      toggleSelectAllBtn.classList.remove('menu-manager-btn-secondary');
      toggleSelectAllBtn.classList.add('menu-manager-btn-success');
      showInfo('已取消全选');
    }
    
    updateInputAndJson();
  }

  // 查找指定菜单项的根节点ID
  function findRootNodeId(itemId) {
    if (!currentMenuData || !currentMenuData.data) return null;

    function findItemPath(items, targetId, path = []) {
      for (let item of items) {
        const currentPath = [...path, item.id];
        
        if (item.id === targetId) {
          // 找到目标项，返回路径中的第一个ID（根节点）
          return currentPath[0];
        }
        
        if (item.children && item.children.length > 0) {
          const result = findItemPath(item.children, targetId, currentPath);
          if (result) return result;
        }
      }
      return null;
    }

    return findItemPath(currentMenuData.data, itemId);
  }

  // 获取根节点名称
  function getRootNodeName(rootId) {
    if (!currentMenuData || !currentMenuData.data || !rootId) return '未知';

    const rootItem = currentMenuData.data.find(item => item.id === rootId);
    return rootItem ? (rootItem.name || rootItem.code || '未命名') : '未知';
  }

  // 全选所有可见菜单项
  function selectAllVisibleMenus() {
    // 清空当前选择
    selectedItems.clear();
    
    // 获取所有可见的菜单项
    const visibleMenuItems = document.querySelectorAll('.menu-item:not([style*="display: none"])');
    
    visibleMenuItems.forEach(menuElement => {
      const itemId = menuElement.dataset.itemId;
      if (itemId) {
        selectedItems.add(itemId);
        menuElement.classList.add('multi-selected');
        menuElement.classList.remove('selected');
      }
    });
    
    saveSelectedItems();
  }

  // 显示菜单项详情弹框
  function showItemDetailModal(item) {
    const detailModal = document.createElement('div');
    detailModal.className = 'menu-manager-modal';
    detailModal.style.zIndex = '10002';

    detailModal.innerHTML = `
            <div class="menu-manager-modal-content" style="width: 60%; max-width: 800px; height: auto; max-height: 90%;">
                <div class="menu-manager-header-bar">
                    <h1 class="menu-manager-title" style="font-size: 18px;">菜单项详情</h1>
                    <button class="close-btn" id="detailCloseBtn"></button>
                </div>
                <div style="padding: 20px; overflow-y: auto; flex: 1;">
                    <div class="detail-form">
                        <div class="form-row">
                            <label class="form-label">资源名称</label>
                            <input class="form-input" value="${item.name || ''}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">编码</label>
                            <input class="form-input" value="${item.code || ''}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">路由</label>
                            <input class="form-input" value="${item.routingUrl || ''}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">交互方式</label>
                            <input class="form-input" value="${item.displayLocation || ''}" readonly>
                        </div>
                        <div class="form-row-group">
                            <div class="form-row half">
                                <label class="form-label">常用模块</label>
                                <input class="form-input" value="${item.commonModule === '1' ? '是' : '否'}" readonly>
                            </div>
                            <div class="form-row half">
                                <label class="form-label">是否隐藏</label>
                                <input class="form-input" value="${item.deleteFlag ? '是' : '否'}" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <label class="form-label">注入Token</label>
                            <input class="form-input" value="${item.injectToken ? '是' : '否'}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">权限验证</label>
                            <input class="form-input" value="${item.checkPermission ? '是' : '否'}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">资源地址</label>
                            <input class="form-input" value="${item.activeRule || ''}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">图标</label>
                            <input class="form-input" value="${item.iconClass || ''}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">排序</label>
                            <input class="form-input" value="${item.sortNo || ''}" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">备注</label>
                            <textarea class="form-textarea" readonly>${item.memo || ''}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;

    document.body.appendChild(detailModal);

    // 绑定关闭事件
    const detailCloseBtn = detailModal.querySelector('#detailCloseBtn');
    if (detailCloseBtn) {
      detailCloseBtn.addEventListener('click', () => {
        detailModal.remove();
      });
    }

    detailModal.addEventListener('click', (e) => {
      if (e.target === detailModal) {
        detailModal.remove();
      }
    });
  }

  // 搜索功能实现
  function handleSearch() {
    const searchInput = document.getElementById('menuSearchInput');
    if (!searchInput || !(searchInput instanceof HTMLInputElement)) return;

    searchQuery = searchInput.value.trim().toLowerCase();

    if (!searchQuery) {
      clearSearchHighlights();
      updateSearchCounter();
      // 搜索清空后，更新按钮状态
      updateSelectAllButtonState();
      return;
    }

    performSearch();
  }

  function performSearch() {
    clearSearchHighlights();
    searchMatches = [];
    currentMatchIndex = -1;

    if (!currentMenuData || !currentMenuData.data) return;

    // 递归搜索所有菜单项
    function searchInItems(items) {
      items.forEach(item => {
        if (isItemMatch(item)) {
          const menuElement = document.querySelector(`[data-item-id="${item.id}"]`);
          if (menuElement) {
            searchMatches.push({
              item: item,
              element: menuElement
            });
          }
        }

        if (item.children && item.children.length > 0) {
          searchInItems(item.children);
        }
      });
    }

    searchInItems(currentMenuData.data);
    updateSearchCounter();

    if (searchMatches.length > 0) {
      currentMatchIndex = 0;
      highlightCurrentMatch();
    }
  }

  function isItemMatch(item) {
    const searchFields = [
      item.name,
      item.code,
      item.activeRule,
      item.activeRule2,
      item.routingUrl,
      item.memo
    ];

    return searchFields.some(field =>
      field && field.toString().toLowerCase().includes(searchQuery)
    );
  }

  function clearSearchHighlights() {
    document.querySelectorAll('.menu-item.search-highlight').forEach(el => {
      el.classList.remove('search-highlight');
    });
  }

  function highlightCurrentMatch() {
    clearSearchHighlights();

    if (currentMatchIndex >= 0 && currentMatchIndex < searchMatches.length) {
      const match = searchMatches[currentMatchIndex];
      match.element.classList.add('search-highlight');

      // 滚动到当前匹配项
      match.element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }

    updateSearchCounter();
  }

  function navigateSearchResults(direction) {
    if (searchMatches.length === 0) return;

    if (direction === 'next') {
      currentMatchIndex = (currentMatchIndex + 1) % searchMatches.length;
    } else if (direction === 'prev') {
      currentMatchIndex = currentMatchIndex <= 0 ? searchMatches.length - 1 : currentMatchIndex - 1;
    }

    highlightCurrentMatch();
  }

  function updateSearchCounter() {
    const searchCounter = document.getElementById('searchCounter');
    const prevBtn = document.getElementById('prevMatchBtn');
    const nextBtn = document.getElementById('nextMatchBtn');

    if (searchCounter) {
      if (searchMatches.length === 0) {
        searchCounter.textContent = searchQuery ? '0/0' : '0/0';
      } else {
        searchCounter.textContent = `${currentMatchIndex + 1}/${searchMatches.length}`;
      }
    }

    // 更新按钮状态
    const hasMatches = searchMatches.length > 0;
    if (prevBtn && prevBtn instanceof HTMLButtonElement) {
      prevBtn.disabled = !hasMatches;
    }
    if (nextBtn && nextBtn instanceof HTMLButtonElement) {
      nextBtn.disabled = !hasMatches;
    }
  }

  // 自动定位到当前路由对应的菜单项
  function autoLocateCurrentRoute() {
    if (!currentMenuData || !currentMenuData.data) {
      console.log('没有菜单数据，无法自动定位');
      showWarning('没有菜单数据，无法自动定位');
      return;
    }

    // 获取当前页面路径
    const currentPath = window.location.pathname;
    const currentHash = window.location.hash;
    const currentSearch = window.location.search;
    const currentFullUrl = window.location.href;

    console.log('开始自动定位，当前路径信息:', {
      pathname: currentPath,
      hash: currentHash,
      search: currentSearch,
      href: currentFullUrl
    });

    // 查找匹配的菜单项
    const matchedItem = findMatchingMenuItem(currentMenuData.data, currentPath, currentHash, currentSearch);

    if (matchedItem) {
      console.log('找到匹配的菜单项:', matchedItem);

      // 清空之前的选择
      clearAllSelections();

      // 选中匹配的菜单项
      const menuElement = document.querySelector(`[data-item-id="${matchedItem.id}"]`);
      if (menuElement) {
        selectItem(matchedItem.id, menuElement);
        updateInputAndJson();

        // 滚动到该菜单项
        setTimeout(() => {
          menuElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });

          // 添加自动定位高亮效果
          menuElement.classList.add('auto-locate-highlight');
          setTimeout(() => {
            menuElement.classList.remove('auto-locate-highlight');
          }, 4000);
        }, 100);

        // 显示详细的匹配信息
        const matchInfo = getMatchInfo(matchedItem, currentFullUrl, currentPath);
        showSuccess(`✅ 已自动定位到菜单项: ${matchedItem.name || matchedItem.code}\n${matchInfo}`);
      } else {
        console.warn('找到匹配菜单项但未找到对应DOM元素');
        showWarning('找到匹配菜单项但界面元素未找到，请刷新页面重试');
      }
    } else {
      console.log('未找到匹配当前路由的菜单项');
      showInfo(`🔍 未找到匹配当前路由的菜单项\n当前URL: ${currentFullUrl}\n建议检查菜单项的 routingUrl 字段是否正确配置`);
    }
  }

  // 获取匹配信息的辅助函数
  function getMatchInfo(item, currentFullUrl, currentPath) {
    const matchFields = [];

    if (item.routingUrl) {
      const matchResult = checkRouteMatch(item.routingUrl, currentPath, currentFullUrl, window.location.origin);
      if (matchResult.isMatch) {
        matchFields.push(`📍 RoutingUrl: ${item.routingUrl} (${matchResult.type})`);
      }
    }

    if (item.activeRule) {
      const matchResult = checkRouteMatch(item.activeRule, currentPath, currentFullUrl, window.location.origin);
      if (matchResult.isMatch) {
        matchFields.push(`🎯 ActiveRule: ${item.activeRule} (${matchResult.type})`);
      }
    }

    if (item.activeRule2) {
      const matchResult = checkRouteMatch(item.activeRule2, currentPath, currentFullUrl, window.location.origin);
      if (matchResult.isMatch) {
        matchFields.push(`🎯 ActiveRule2: ${item.activeRule2} (${matchResult.type})`);
      }
    }

    return matchFields.length > 0 ? `匹配字段: ${matchFields.join(', ')}` : '匹配成功';
  }

  // 查找匹配当前路由的菜单项
  function findMatchingMenuItem(items, currentPath, currentHash, currentSearch) {
    // 获取完整的当前URL（包含域名）
    const currentFullUrl = window.location.href;
    const currentOrigin = window.location.origin;

    console.log('开始查找匹配菜单项:', {
      currentPath,
      currentFullUrl,
      currentOrigin
    });

    // 存储所有匹配结果，按匹配优先级排序
    const matches = [];

    function searchItems(items, depth = 0) {
      for (let item of items) {
        // 优先检查 routingUrl 字段
        if (item.routingUrl) {
          const matchResult = checkRouteMatch(item.routingUrl, currentPath, currentFullUrl, currentOrigin);
          if (matchResult.isMatch) {
            matches.push({
              item: item,
              priority: matchResult.priority,
              matchType: `routingUrl-${matchResult.type}`,
              depth: depth
            });
            console.log('找到routingUrl匹配:', {
              item: item.name || item.code,
              routingUrl: item.routingUrl,
              matchType: matchResult.type,
              priority: matchResult.priority
            });
          }
        }

        // 检查其他路由字段（优先级较低）
        const otherRouteFields = [
          { field: item.activeRule, name: 'activeRule' },
          { field: item.activeRule2, name: 'activeRule2' },
          { field: item.url, name: 'url' },
          { field: item.path, name: 'path' }
        ];

        for (let routeInfo of otherRouteFields) {
          if (routeInfo.field) {
            const matchResult = checkRouteMatch(routeInfo.field, currentPath, currentFullUrl, currentOrigin);
            if (matchResult.isMatch) {
              matches.push({
                item: item,
                priority: matchResult.priority - 10, // 降低其他字段的优先级
                matchType: `${routeInfo.name}-${matchResult.type}`,
                depth: depth
              });
            }
          }
        }

        // 递归检查子菜单
        if (item.children && item.children.length > 0) {
          searchItems(item.children, depth + 1);
        }
      }
    }

    searchItems(items);

    // 按优先级排序，优先级高的在前，深度浅的在前
    matches.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // 优先级高的在前
      }
      return a.depth - b.depth; // 深度浅的在前
    });

    console.log('所有匹配结果:', matches);

    // 返回最佳匹配
    return matches.length > 0 ? matches[0].item : null;
  }

  // 检查路由匹配的详细函数
  function checkRouteMatch(routeField, currentPath, currentFullUrl, currentOrigin) {
    if (!routeField || typeof routeField !== 'string') {
      return { isMatch: false, priority: 0, type: 'none' };
    }

    const cleanRoute = routeField.trim();

    // 1. 完整URL精确匹配（最高优先级）
    if (cleanRoute === currentFullUrl) {
      return { isMatch: true, priority: 100, type: 'exact-full-url' };
    }

    // 2. 处理相对路径和绝对路径
    let routePath = cleanRoute;

    // 如果是完整URL，提取路径部分
    if (cleanRoute.startsWith('http://') || cleanRoute.startsWith('https://')) {
      try {
        const routeUrl = new URL(cleanRoute);
        routePath = routeUrl.pathname + routeUrl.search + routeUrl.hash;
      } catch (e) {
        console.warn('无法解析URL:', cleanRoute);
        return { isMatch: false, priority: 0, type: 'invalid-url' };
      }
    } else if (cleanRoute.startsWith(currentOrigin)) {
      // 移除域名部分
      routePath = cleanRoute.substring(currentOrigin.length);
    }

    // 确保路径以 / 开头
    if (!routePath.startsWith('/')) {
      routePath = '/' + routePath;
    }

    // 3. 路径精确匹配（高优先级）
    if (routePath === currentPath) {
      return { isMatch: true, priority: 90, type: 'exact-path' };
    }

    // 4. 去除参数后的路径匹配
    const currentPathWithoutParams = currentPath.split('?')[0].split('#')[0];
    const routePathWithoutParams = routePath.split('?')[0].split('#')[0];

    if (routePathWithoutParams === currentPathWithoutParams) {
      return { isMatch: true, priority: 80, type: 'path-without-params' };
    }

    // 5. 当前路径包含菜单路径（中等优先级）
    if (currentPath.includes(routePathWithoutParams) && routePathWithoutParams.length > 1) {
      return { isMatch: true, priority: 70, type: 'current-contains-route' };
    }

    // 6. 菜单路径包含当前路径（中等优先级）
    if (routePathWithoutParams.includes(currentPathWithoutParams) && currentPathWithoutParams.length > 1) {
      return { isMatch: true, priority: 60, type: 'route-contains-current' };
    }

    // 7. 路径段匹配（较低优先级）
    const currentSegments = currentPathWithoutParams.split('/').filter(s => s);
    const routeSegments = routePathWithoutParams.split('/').filter(s => s);

    if (routeSegments.length > 0 && currentSegments.length >= routeSegments.length) {
      const matchingSegments = routeSegments.filter((segment, index) => {
        return currentSegments[index] === segment;
      });

      if (matchingSegments.length === routeSegments.length) {
        return { isMatch: true, priority: 50, type: 'segment-match' };
      }
    }

    // 8. 部分段匹配（最低优先级）
    if (routeSegments.length > 0 && currentSegments.length > 0) {
      const commonSegments = routeSegments.filter(segment =>
        currentSegments.includes(segment)
      );

      if (commonSegments.length > 0) {
        return { isMatch: true, priority: 30, type: 'partial-segment-match' };
      }
    }

    return { isMatch: false, priority: 0, type: 'no-match' };
  }



  // 初始化加载本地存储数据
  initializeFromStorage();

  console.log('菜单权限管理器已加载');
})(); 
