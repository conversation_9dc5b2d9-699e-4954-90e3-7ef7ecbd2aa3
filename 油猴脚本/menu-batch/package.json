{"name": "cyber-menu-matrix", "version": "2.0.77", "description": "赛博朋克风格的菜单权限批量管理工具，支持智能路由匹配和批量编辑", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:prod", "build:dev": "NODE_ENV=development vite build && npm run post-build", "build:prod": "NODE_ENV=production vite build && npm run post-build", "post-build": "node scripts/post-build.js", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"preact": "^10.19.0", "zustand": "^4.4.0", "clsx": "^2.0.0", "lucide-preact": "^0.300.0"}, "devDependencies": {"@preact/preset-vite": "^2.5.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-preact": "^0.1.0", "framer-motion": "^10.16.0", "gsap": "^3.12.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "terser": "^5.24.0", "typescript": "^5.3.0", "vite": "^5.0.0"}, "keywords": ["tampermonkey", "userscript", "menu-management", "cyberpunk", "preact", "typescript"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/cyber-menu-matrix.git"}, "bugs": {"url": "https://github.com/your-username/cyber-menu-matrix/issues"}, "homepage": "https://github.com/your-username/cyber-menu-matrix"}