const fs = require('fs');
const path = require('path');

async function postBuild() {
  const distPath = path.resolve(__dirname, '../dist');
  
  // 确保 dist 目录存在
  if (!fs.existsSync(distPath)) {
    console.error('❌ Dist directory not found!');
    return;
  }
  
  const files = fs.readdirSync(distPath);
  
  // 找到生成的JS文件
  const jsFile = files.find(file => file.endsWith('.user.js'));
  if (!jsFile) {
    console.error('❌ No .user.js file found!');
    return;
  }
  
  const filePath = path.join(distPath, jsFile);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 确保文件以闭包结束
  if (!content.includes('})();')) {
    content += '\n})();';
  }
  
  // 添加错误处理包装
  content = content.replace(
    /^(\/\/ ==UserScript==[\s\S]*?\/\/ ==\/UserScript==\n)/,
    `$1
(function() {
  'use strict';
  
  try {
`
  );
  
  content += `
  } catch (error) {
    console.error('[Cyber Menu Matrix] Fatal error:', error);
    if (typeof GM_notification !== 'undefined') {
      GM_notification({
        title: 'Cyber Menu Matrix Error',
        text: 'Script failed to initialize. Check console for details.',
        timeout: 5000
      });
    }
  }
})();`;
  
  // 写回文件
  fs.writeFileSync(filePath, content);
  
  // 显示构建信息
  const stats = fs.statSync(filePath);
  const sizeKB = (stats.size / 1024).toFixed(2);
  
  console.log(`✅ Build completed!`);
  console.log(`📦 File: ${jsFile}`);
  console.log(`📏 Size: ${sizeKB} KB`);
  console.log(`🔗 Path: ${filePath}`);
  
  // 检查文件大小警告
  if (stats.size > 100 * 1024) { // 100KB
    console.warn(`⚠️ Warning: File size is large (${sizeKB} KB). Consider optimizing.`);
  }
}

// 执行构建后处理
postBuild().catch(error => {
  console.error('❌ Post-build failed:', error);
  process.exit(1);
}); 