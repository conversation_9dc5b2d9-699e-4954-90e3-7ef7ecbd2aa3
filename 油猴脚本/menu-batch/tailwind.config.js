/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // 赛博朋克色彩系统
        cyber: {
          primary: '#00FFFF',    // 霓虹青
          secondary: '#FF006E',  // 霓虹粉
          accent: '#39FF14',     // 矩阵绿
          warning: '#FFD700',    // 金黄
          bg: '#0A0A0A',         // 深黑
          panel: '#1A1A2E',      // 深蓝黑
          border: '#2A2A3E',     // 边框色
        },
        // Tailwind 默认色彩的赛博朋克变体
        gray: {
          950: '#0A0A0A',
          900: '#1A1A2E',
          800: '#2A2A3E',
          700: '#3A3A4E',
          600: '#4A4A5E',
        },
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'Fira Code', 'Courier New', 'monospace'],
        cyber: ['Orbitron', 'monospace'],
      },
      fontSize: {
        'xs': '0.75rem',
        'sm': '0.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
      },
      spacing: {
        'xs': '4px',
        'sm': '8px',
        'md': '16px',
        'lg': '24px',
        'xl': '32px',
      },
      animation: {
        'cyber-pulse': 'cyber-pulse 2s ease-in-out infinite',
        'matrix-rain': 'matrix-rain 0.1s linear infinite',
        'scan-line': 'scan-line 2s linear infinite',
        'glow-pulse': 'glow-pulse 2s ease-in-out infinite',
        'fade-in': 'fade-in 0.3s ease-out',
        'slide-up': 'slide-up 0.3s ease-out',
        'scale-in': 'scale-in 0.2s ease-out',
      },
      keyframes: {
        'cyber-pulse': {
          '0%, 100%': { 
            boxShadow: '0 0 5px currentColor',
            filter: 'blur(1px)'
          },
          '50%': { 
            boxShadow: '0 0 20px currentColor, 0 0 30px currentColor',
            filter: 'blur(3px)'
          },
        },
        'matrix-rain': {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(100vh)' },
        },
        'scan-line': {
          '0%': { backgroundPosition: '0% 0%' },
          '100%': { backgroundPosition: '100% 100%' },
        },
        'glow-pulse': {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.5' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'slide-up': {
          '0%': { 
            opacity: '0',
            transform: 'translateY(20px)'
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        'scale-in': {
          '0%': { 
            opacity: '0',
            transform: 'scale(0.9)'
          },
          '100%': { 
            opacity: '1',
            transform: 'scale(1)'
          },
        },
      },
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
      },
      boxShadow: {
        'cyber': '0 0 10px currentColor',
        'cyber-lg': '0 0 20px currentColor, 0 0 30px currentColor',
        'inner-cyber': 'inset 0 0 10px currentColor',
      },
    },
  },
  plugins: [
    // 自定义插件：赛博朋克工具类
    function({ addUtilities }) {
      const newUtilities = {
        '.cyber-border': {
          border: '1px solid #00FFFF',
          boxShadow: '0 0 10px #00FFFF',
        },
        '.cyber-glow': {
          textShadow: '0 0 10px currentColor',
        },
        '.cyber-bg': {
          background: 'linear-gradient(135deg, #1A1A2E 0%, #0A0A0A 100%)',
        },
        '.matrix-bg': {
          background: `
            radial-gradient(circle at 25% 25%, #39FF14 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, #00FFFF 0%, transparent 50%),
            linear-gradient(135deg, #1A1A2E 0%, #0A0A0A 100%)
          `,
        },
        '.scan-lines': {
          background: `
            linear-gradient(
              90deg,
              transparent 0%,
              rgba(0, 255, 255, 0.1) 50%,
              transparent 100%
            )
          `,
          backgroundSize: '100px 100%',
          animation: 'scan-line 2s linear infinite',
        },
      };
      addUtilities(newUtilities);
    }
  ],
}; 