import { useEffect } from 'preact/hooks';
import { CyberPanel } from '@components/CyberPanel';
import { useMenuStore } from '@stores/menuStore';

interface AppProps {
  onClose: () => void;
}

export function App({ onClose }: AppProps) {
  const loadMenuData = useMenuStore(state => state.loadMenuData);
  const loadFromStorage = useMenuStore(state => state.loadFromStorage);
  const setCurrentPath = useMenuStore(state => state.setCurrentPath);

  useEffect(() => {
    // 设置当前路径
    setCurrentPath(window.location.pathname);
    
    // 尝试从本地存储加载数据
    loadFromStorage();
    
    // 然后从API加载最新数据
    loadMenuData();
  }, []);

  return (
    <div className="w-full h-full flex items-center justify-center p-4">
      <CyberPanel onClose={onClose} />
    </div>
  );
} 