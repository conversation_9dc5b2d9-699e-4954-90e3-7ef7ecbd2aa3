@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局赛博朋克样式变量 */
:root {
  /* 赛博朋克色彩系统 */
  --cyber-primary: #00FFFF;    /* 霓虹青 */
  --cyber-secondary: #FF006E;  /* 霓虹粉 */
  --cyber-accent: #39FF14;     /* 矩阵绿 */
  --cyber-warning: #FFD700;    /* 金黄 */
  --cyber-bg: #0A0A0A;         /* 深黑 */
  --cyber-panel: #1A1A2E;      /* 深蓝黑 */
  --cyber-border: #2A2A3E;     /* 边框色 */
  
  /* 动画变量 */
  --anim-fast: 150ms;
  --anim-normal: 300ms;
  --anim-slow: 500ms;
  
  /* 尺寸变量 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 字体 */
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
  --font-cyber: 'Orbitron', monospace;
}

/* 基础重置和字体设置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-mono);
  background: var(--cyber-bg);
  color: var(--cyber-primary);
  overflow-x: hidden;
}

/* 赛博朋克滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--cyber-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--cyber-primary), var(--cyber-accent));
  border-radius: 4px;
  box-shadow: 0 0 10px var(--cyber-primary);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--cyber-accent), var(--cyber-secondary));
  box-shadow: 0 0 15px var(--cyber-accent);
}

/* 选择文本样式 */
::selection {
  background: var(--cyber-secondary);
  color: white;
  text-shadow: 0 0 10px var(--cyber-secondary);
}

/* 全局动画 */
@keyframes cyber-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px currentColor;
    filter: blur(1px);
  }
  50% { 
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    filter: blur(3px);
  }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes scan-line {
  0% { background-position: 0% 0%; }
  100% { background-position: 100% 100%; }
}

@keyframes glow-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slide-up {
  0% { 
    opacity: 0;
    transform: translateY(20px);
  }
  100% { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% { 
    opacity: 0;
    transform: scale(0.9);
  }
  100% { 
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

/* 工具类 */
.cyber-border {
  border: 1px solid var(--cyber-primary);
  box-shadow: 0 0 10px var(--cyber-primary);
}

.cyber-glow {
  text-shadow: 0 0 10px currentColor;
}

.cyber-bg {
  background: linear-gradient(135deg, var(--cyber-panel) 0%, var(--cyber-bg) 100%);
}

.matrix-bg {
  background: 
    radial-gradient(circle at 25% 25%, var(--cyber-accent) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, var(--cyber-primary) 0%, transparent 50%),
    linear-gradient(135deg, var(--cyber-panel) 0%, var(--cyber-bg) 100%);
}

.scan-lines {
  background: 
    linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 255, 255, 0.1) 50%,
      transparent 100%
    );
  background-size: 100px 100%;
  animation: scan-line 2s linear infinite;
}

.cyber-input {
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid var(--cyber-border);
  color: var(--cyber-primary);
  padding: 8px 12px;
  border-radius: 4px;
  font-family: var(--font-mono);
  transition: all var(--anim-normal) ease;
}

.cyber-input:focus {
  outline: none;
  border-color: var(--cyber-primary);
  box-shadow: 0 0 10px var(--cyber-primary);
  background: rgba(26, 26, 46, 1);
}

.cyber-button {
  background: linear-gradient(135deg, var(--cyber-primary), var(--cyber-accent));
  border: 1px solid var(--cyber-primary);
  color: var(--cyber-bg);
  padding: 8px 16px;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-weight: bold;
  cursor: pointer;
  transition: all var(--anim-normal) ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cyber-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px var(--cyber-primary);
  background: linear-gradient(135deg, var(--cyber-accent), var(--cyber-secondary));
}

.cyber-button:active {
  transform: translateY(0);
}

.cyber-button--secondary {
  background: transparent;
  color: var(--cyber-primary);
  border: 1px solid var(--cyber-primary);
}

.cyber-button--secondary:hover {
  background: var(--cyber-primary);
  color: var(--cyber-bg);
}

.cyber-card {
  background: rgba(26, 26, 46, 0.9);
  border: 1px solid var(--cyber-border);
  border-radius: 8px;
  padding: 16px;
  transition: all var(--anim-normal) ease;
  position: relative;
  overflow: hidden;
}

.cyber-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transition: left var(--anim-slow) ease;
}

.cyber-card:hover::before {
  left: 100%;
}

.cyber-card:hover {
  border-color: var(--cyber-primary);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* 当前页面匹配项的特殊样式 */
.current-page-match {
  border-left: 4px solid var(--cyber-warning) !important;
  background: linear-gradient(90deg, 
    rgba(255, 215, 0, 0.1) 0%, 
    transparent 100%) !important;
  position: relative;
}

.current-page-match::before {
  content: "📍";
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  animation: glow-pulse 2s infinite;
}

/* 选择状态 */
.menu-item--selected {
  background: rgba(255, 0, 110, 0.2) !important;
  border-color: var(--cyber-secondary) !important;
  box-shadow: 0 0 15px rgba(255, 0, 110, 0.5) !important;
}

.menu-item--multi-selected {
  background: rgba(57, 255, 20, 0.2) !important;
  border-color: var(--cyber-accent) !important;
  box-shadow: 0 0 15px rgba(57, 255, 20, 0.5) !important;
}

/* 加载动画 */
.cyber-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--cyber-border);
  border-top: 2px solid var(--cyber-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 矩阵雨背景效果 */
.matrix-rain-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.matrix-char {
  position: absolute;
  font-family: var(--font-mono);
  font-size: 14px;
  color: var(--cyber-accent);
  opacity: 0.7;
  animation: matrix-rain 2s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --spacing-sm: 6px;
    --spacing-md: 12px;
    --spacing-lg: 18px;
    --spacing-xl: 24px;
  }
  
  .cyber-card {
    padding: 12px;
  }
  
  .cyber-button {
    padding: 6px 12px;
    font-size: 14px;
  }
  
  .cyber-input {
    padding: 6px 10px;
    font-size: 14px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --cyber-bg: #000000;
    --cyber-panel: #000000;
    --cyber-primary: #FFFFFF;
    --cyber-secondary: #FFFFFF;
    --cyber-accent: #FFFFFF;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
} 