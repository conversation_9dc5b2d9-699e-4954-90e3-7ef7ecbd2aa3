import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { MenuItem, RouteMatchResult, StorageData } from '@types/menu';

interface MenuStore {
  // 数据状态
  originalData: MenuItem[];
  modifiedData: MenuItem[];
  selectedItems: string[];
  
  // UI状态
  searchQuery: string;
  showDeleted: boolean;
  currentPath: string;
  expandedNodes: Set<string>;
  
  // 加载状态
  isLoading: boolean;
  error: string | null;
  
  // 动作方法
  loadMenuData: () => Promise<void>;
  updateMenuItem: (id: string, updates: Partial<MenuItem>) => void;
  batchUpdateActiveRule: (value: string) => void;
  toggleSelection: (id: string) => void;
  clearSelection: () => void;
  selectAll: () => void;
  
  // 搜索和过滤
  setSearchQuery: (query: string) => void;
  toggleShowDeleted: () => void;
  getFilteredItems: () => MenuItem[];
  
  // 智能路由匹配
  findMatchingItems: (path: string) => RouteMatchResult[];
  highlightCurrentPage: () => void;
  setCurrentPath: (path: string) => void;
  
  // 树状态管理
  toggleNodeExpansion: (nodeId: string) => void;
  expandAll: () => void;
  collapseAll: () => void;
  
  // 数据持久化
  saveToStorage: () => void;
  loadFromStorage: () => void;
  resetToOriginal: () => void;
  
  // 导出功能
  exportSelectedAsJSON: () => string;
  exportAllAsJSON: () => string;
}

// 智能路由匹配算法
const SmartRouteDetector = {
  isRouteMatch(currentPath: string, routePattern?: string): boolean {
    if (!routePattern) return false;
    
    // 支持通配符匹配
    if (routePattern.includes('*')) {
      const regex = new RegExp(
        routePattern.replace(/\*/g, '.*').replace(/\//g, '\\/')
      );
      return regex.test(currentPath);
    }
    
    // 精确匹配或前缀匹配
    return currentPath === routePattern || 
           currentPath.startsWith(routePattern + '/');
  },

  calculateMatchScore(currentPath: string, item: MenuItem, field: string): number {
    const value = item[field as keyof MenuItem] as string;
    if (!value) return 0;

    if (currentPath === value) return 100; // 精确匹配
    if (currentPath.startsWith(value + '/')) return 80; // 前缀匹配
    if (this.isRouteMatch(currentPath, value)) return 60; // 通配符匹配
    if (currentPath.includes(value) || value.includes(currentPath)) return 40; // 模糊匹配
    
    return 0;
  }
};

// API 服务
const apiService = {
  async fetchMenuData(): Promise<MenuItem[]> {
    const baseUrl = window.location.origin;
    
    // 获取用户信息判断权限
    const user = this.getUserInfo();
    const isAdmin = this.isAdminUser(user);
    
    // 根据权限选择接口
    const endpoint = isAdmin 
      ? `${baseUrl}/dxdsapi/PrivilegeProduct/privilegeTree/1?projectCode=1`
      : `${baseUrl}/dxdsapi/PrivilegeProduct/myPrivilege/1`;
    
    try {
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      // 处理不同的响应格式
      if (result.success === false) {
        throw new Error(result.message || '获取菜单数据失败');
      }
      
      return result.data || result || [];
    } catch (error) {
      console.error('[Menu API] Fetch failed:', error);
      throw error;
    }
  },

  getUserInfo() {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  },

  isAdminUser(user: any): boolean {
    if (!user) return false;
    
    // 管理员条件
    if (user.id === '1') return true;
    
    if (user.roleList && Array.isArray(user.roleList)) {
      return user.roleList.some((role: any) => role.code === 'administrator');
    }
    
    return false;
  }
};

// 存储服务
const storageService = {
  save(data: StorageData) {
    try {
      GM_setValue('cyber_menu_data', JSON.stringify(data));
    } catch (error) {
      console.warn('[Storage] Save failed:', error);
      // 降级到 localStorage
      localStorage.setItem('cyber_menu_data', JSON.stringify(data));
    }
  },

  load(): StorageData | null {
    try {
      const data = GM_getValue('cyber_menu_data', null);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('[Storage] Load failed, trying localStorage:', error);
      try {
        const data = localStorage.getItem('cyber_menu_data');
        return data ? JSON.parse(data) : null;
      } catch {
        return null;
      }
    }
  },

  clear() {
    try {
      GM_deleteValue('cyber_menu_data');
    } catch {
      localStorage.removeItem('cyber_menu_data');
    }
  }
};

export const useMenuStore = create<MenuStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      originalData: [],
      modifiedData: [],
      selectedItems: [],
      searchQuery: '',
      showDeleted: false,
      currentPath: window.location.pathname,
      expandedNodes: new Set(),
      isLoading: false,
      error: null,

      // 加载菜单数据
      loadMenuData: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const data = await apiService.fetchMenuData();
          set((state) => {
            state.originalData = data;
            state.modifiedData = structuredClone(data);
            state.isLoading = false;
          });
          
          // 保存到本地存储
          get().saveToStorage();
          
          // 智能高亮当前页面
          get().highlightCurrentPage();
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.error = error instanceof Error ? error.message : '加载失败';
          });
        }
      },

      // 更新菜单项
      updateMenuItem: (id, updates) => {
        set((state) => {
          const findAndUpdate = (items: MenuItem[]): boolean => {
            for (const item of items) {
              if (String(item.id) === String(id)) {
                Object.assign(item, updates);
                return true;
              }
              if (item.children && findAndUpdate(item.children)) {
                return true;
              }
            }
            return false;
          };
          
          findAndUpdate(state.modifiedData);
        });
        
        // 自动保存
        get().saveToStorage();
      },

      // 批量更新activeRule
      batchUpdateActiveRule: (value) => {
        const { selectedItems } = get();
        
        selectedItems.forEach(id => {
          get().updateMenuItem(id, { activeRule: value });
        });
      },

      // 切换选择状态
      toggleSelection: (id) => {
        set((state) => {
          const index = state.selectedItems.indexOf(id);
          if (index > -1) {
            state.selectedItems.splice(index, 1);
          } else {
            state.selectedItems.push(id);
          }
        });
      },

      // 清除选择
      clearSelection: () => {
        set((state) => {
          state.selectedItems = [];
        });
      },

      // 全选
      selectAll: () => {
        const filteredItems = get().getFilteredItems();
        const allIds = filteredItems.map(item => String(item.id));
        
        set((state) => {
          state.selectedItems = allIds;
        });
      },

      // 设置搜索查询
      setSearchQuery: (query) => {
        set((state) => {
          state.searchQuery = query;
        });
      },

      // 切换显示删除项
      toggleShowDeleted: () => {
        set((state) => {
          state.showDeleted = !state.showDeleted;
        });
      },

      // 获取过滤后的项目
      getFilteredItems: () => {
        const { modifiedData, searchQuery, showDeleted } = get();
        
        const filterItems = (items: MenuItem[]): MenuItem[] => {
          return items.filter(item => {
            // 删除项过滤
            if (!showDeleted && item.deleteFlag) return false;
            
            // 搜索过滤
            if (searchQuery) {
              const query = searchQuery.toLowerCase();
              const searchFields = [
                item.name, item.code, item.activeRule, 
                item.routingUrl, item.url, item.path
              ];
              
              const matches = searchFields.some(field => 
                field && String(field).toLowerCase().includes(query)
              );
              
              if (!matches) return false;
            }
            
            return true;
          }).map(item => ({
            ...item,
            children: item.children ? filterItems(item.children) : undefined
          }));
        };
        
        return filterItems(modifiedData);
      },

      // 智能路由匹配
      findMatchingItems: (path) => {
        const { modifiedData } = get();
        const matches: RouteMatchResult[] = [];
        
        const searchItems = (items: MenuItem[]) => {
          items.forEach(item => {
            const matchFields = ['routingUrl', 'activeRule', 'activeRule2', 'url', 'path'];
            
            matchFields.forEach(field => {
              const score = SmartRouteDetector.calculateMatchScore(path, item, field);
              if (score > 0) {
                matches.push({
                  item,
                  matchType: score >= 100 ? 'exact' : 
                           score >= 80 ? 'prefix' : 
                           score >= 60 ? 'wildcard' : 'fuzzy',
                  matchField: field,
                  score
                });
              }
            });
            
            if (item.children) {
              searchItems(item.children);
            }
          });
        };
        
        searchItems(modifiedData);
        
        // 按分数排序
        return matches.sort((a, b) => b.score - a.score);
      },

      // 高亮当前页面
      highlightCurrentPage: () => {
        const { currentPath } = get();
        const matches = get().findMatchingItems(currentPath);
        
        if (matches.length > 0) {
          // 展开匹配项的父节点
          matches.forEach(match => {
            // TODO: 实现父节点展开逻辑
          });
          
          console.log(`[Smart Router] Found ${matches.length} matches for ${currentPath}`);
        }
      },

      // 设置当前路径
      setCurrentPath: (path) => {
        set((state) => {
          state.currentPath = path;
        });
      },

      // 树节点展开/折叠
      toggleNodeExpansion: (nodeId) => {
        set((state) => {
          if (state.expandedNodes.has(nodeId)) {
            state.expandedNodes.delete(nodeId);
          } else {
            state.expandedNodes.add(nodeId);
          }
        });
      },

      // 展开所有节点
      expandAll: () => {
        const { modifiedData } = get();
        const allNodeIds = new Set<string>();
        
        const collectNodeIds = (items: MenuItem[]) => {
          items.forEach(item => {
            allNodeIds.add(String(item.id));
            if (item.children) {
              collectNodeIds(item.children);
            }
          });
        };
        
        collectNodeIds(modifiedData);
        
        set((state) => {
          state.expandedNodes = allNodeIds;
        });
      },

      // 折叠所有节点
      collapseAll: () => {
        set((state) => {
          state.expandedNodes = new Set();
        });
      },

      // 保存到存储
      saveToStorage: () => {
        const { originalData, modifiedData } = get();
        
        const storageData: StorageData = {
          original: originalData,
          modified: modifiedData,
          metadata: {
            lastFetch: Date.now(),
            version: '2.0.77',
            changes: [], // TODO: 实现变更记录
          },
          preferences: {
            autoSave: true,
            showDeleted: get().showDeleted,
            theme: 'cyberpunk',
          }
        };
        
        storageService.save(storageData);
      },

      // 从存储加载
      loadFromStorage: () => {
        const data = storageService.load();
        if (data) {
          set((state) => {
            state.originalData = data.original;
            state.modifiedData = data.modified;
            state.showDeleted = data.preferences.showDeleted;
          });
        }
      },

      // 重置到原始数据
      resetToOriginal: () => {
        set((state) => {
          state.modifiedData = structuredClone(state.originalData);
          state.selectedItems = [];
        });
        
        get().saveToStorage();
      },

      // 导出选中项为JSON
      exportSelectedAsJSON: () => {
        const { modifiedData, selectedItems } = get();
        
        const findSelectedItems = (items: MenuItem[]): MenuItem[] => {
          const result: MenuItem[] = [];
          
          items.forEach(item => {
            if (selectedItems.includes(String(item.id))) {
              result.push(item);
            }
            if (item.children) {
              const childResults = findSelectedItems(item.children);
              result.push(...childResults);
            }
          });
          
          return result;
        };
        
        const selectedData = findSelectedItems(modifiedData);
        return JSON.stringify(selectedData, null, 2);
      },

      // 导出所有数据为JSON
      exportAllAsJSON: () => {
        const { modifiedData } = get();
        return JSON.stringify(modifiedData, null, 2);
      },
    }))
  )
); 