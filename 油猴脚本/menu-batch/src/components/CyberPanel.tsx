interface CyberPanelProps {
  onClose: () => void;
}

export function CyberPanel({ onClose }: CyberPanelProps) {
  return (
    <div className="w-full max-w-7xl h-full max-h-[90vh] matrix-bg border border-cyber-primary rounded-lg shadow-cyber-lg overflow-hidden">
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-4 border-b border-cyber-border bg-cyber-panel/50">
        <div className="flex items-center space-x-4">
          <div className="text-2xl font-bold cyber-glow text-cyber-primary">
            ▲ CYBER MENU MATRIX v2.0.77
          </div>
          <div className="text-sm text-cyber-accent">
            📍CONTEXT:{window.location.pathname}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="cyber-button--secondary" title="刷新数据">
            ↻
          </button>
          <button className="cyber-button--secondary" title="重置修改">
            RST
          </button>
          <button 
            className="cyber-button--secondary"
            onClick={onClose}
            title="关闭界面"
          >
            ✕
          </button>
        </div>
      </div>

      {/* 搜索栏 */}
      <div className="p-4 border-b border-cyber-border bg-cyber-panel/30">
        <div className="flex items-center space-x-4">
          <div className="flex-1 flex items-center space-x-2">
            <span className="text-cyber-primary">🔍</span>
            <input
              type="text"
              placeholder="输入搜索内容..."
              className="flex-1 cyber-input"
            />
            <button className="cyber-button">SCAN</button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-cyber-accent">📊[0]</span>
            <span className="text-cyber-warning">◤LOG◢</span>
          </div>
        </div>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧模块树 */}
        <div className="w-64 border-r border-cyber-border bg-cyber-panel/20 p-4">
          <h3 className="text-cyber-primary mb-4 cyber-glow">MODULE TREE</h3>
          <div className="space-y-2">
            <div className="cyber-card p-2 text-sm">
              <div className="text-cyber-accent">▣ SYS</div>
              <div className="ml-4 text-cyber-border">├─▫Auth</div>
              <div className="ml-4 text-cyber-border">├─▪Data</div>
              <div className="ml-4 text-cyber-border">└─▪Logs</div>
            </div>
            <div className="cyber-card p-2 text-sm">
              <div className="text-cyber-accent">▣ ADMIN</div>
              <div className="ml-4 text-cyber-border">├─▫Users</div>
              <div className="ml-4 text-cyber-border">└─▪Roles</div>
            </div>
          </div>
        </div>

        {/* 中间菜单列表 */}
        <div className="flex-1 p-4">
          <h3 className="text-cyber-primary mb-4 cyber-glow">MENU LIST (VERTICAL)</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {[
              { name: 'USER-MGMT', id: '1001', status: '●', type: '_micro' },
              { name: 'AUTH-SERVICE', id: '2003', status: '○', type: '_self' },
              { name: 'DATA-CONFIG', id: '3007', status: '●', type: '_iframe' },
              { name: 'CONF-PANEL', id: '4012', status: '○', type: '_blank' },
              { name: 'LOG-VIEWER', id: '5008', status: '●', type: '_micro' },
            ].map((item) => (
              <div key={item.id} className="cyber-card p-3 cursor-pointer hover:border-cyber-primary">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-cyber-accent">▣{item.name}</span>
                    <span className={item.status === '●' ? 'text-cyber-accent' : 'text-gray-500'}>
                      [{item.status}]
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-cyber-border">ID:{item.id}</span>
                    <span className="ml-2 text-cyber-secondary">{item.type}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧详细信息 */}
        <div className="w-80 border-l border-cyber-border bg-cyber-panel/20 p-4">
          <h3 className="text-cyber-primary mb-4 cyber-glow">SELECTED INFO</h3>
          <div className="cyber-card p-3">
            <div className="text-cyber-secondary">█ USER-MGMT</div>
            <div className="text-sm mt-2 space-y-1">
              <div><span className="text-cyber-border">activeRule:</span> <span className="text-cyber-accent">/api/user/*</span></div>
              <div><span className="text-cyber-border">Route:</span> <span className="text-cyber-accent">/admin/user</span></div>
              <div><span className="text-cyber-border">Type:</span> <span className="text-cyber-warning">_micro</span></div>
            </div>
          </div>
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className="p-4 border-t border-cyber-border bg-cyber-panel/50">
        <div className="flex items-center space-x-4">
          <span className="text-cyber-primary">activeRule:</span>
          <input
            type="text"
            placeholder="输入IP/域名地址..."
            className="flex-1 cyber-input"
          />
          <button className="cyber-button">SET</button>
          <button className="cyber-button--secondary">COPY</button>
          <div className="flex items-center space-x-2">
            <span className="text-cyber-accent">💾</span>
            <span className="text-cyber-accent">[AUTO]</span>
          </div>
        </div>
      </div>

      {/* 扫描线效果 */}
      <div className="absolute inset-0 scan-lines pointer-events-none opacity-30"></div>
    </div>
  );
} 