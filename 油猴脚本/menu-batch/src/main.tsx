import { render } from 'preact';
import { App } from './App';
import './styles/globals.css';

// 全局类型声明
declare global {
  interface Window {
    CyberMenuMatrix: {
      init: () => void;
      destroy: () => void;
      version: string;
    };
  }
}

class CyberMenuMatrix {
  private container: HTMLElement | null = null;
  private isInitialized = false;
  private floatButton: HTMLElement | null = null;

  init = () => {
    if (this.isInitialized) return;
    
    console.log('[Cyber Menu Matrix] Initializing v2.0.77...');
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', this.bootstrap);
    } else {
      this.bootstrap();
    }
  };

  private bootstrap = () => {
    try {
      this.createFloatButton();
      this.isInitialized = true;
      console.log('[Cyber Menu Matrix] ✅ Initialized successfully');
    } catch (error) {
      console.error('[Cyber Menu Matrix] ❌ Initialization failed:', error);
      this.showErrorNotification(error as Error);
    }
  };

  private createFloatButton = () => {
    // 检查是否已存在
    const existing = document.getElementById('cyber-menu-float-btn');
    if (existing) {
      existing.remove();
    }

    // 创建悬浮按钮
    this.floatButton = document.createElement('div');
    this.floatButton.id = 'cyber-menu-float-btn';
    this.floatButton.innerHTML = '⚙️';
    this.floatButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #00FFFF, #FF006E);
      color: white;
      border: 2px solid #00FFFF;
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      cursor: pointer;
      z-index: 999998;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      transition: all 0.3s ease;
      user-select: none;
      font-family: monospace;
    `;

    // 悬停效果
    this.floatButton.addEventListener('mouseenter', () => {
      if (this.floatButton) {
        this.floatButton.style.transform = 'scale(1.1)';
        this.floatButton.style.boxShadow = '0 0 30px rgba(0, 255, 255, 0.8)';
      }
    });

    this.floatButton.addEventListener('mouseleave', () => {
      if (this.floatButton) {
        this.floatButton.style.transform = 'scale(1)';
        this.floatButton.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.5)';
      }
    });

    // 点击事件
    this.floatButton.addEventListener('click', this.showMainInterface);

    // 拖拽功能
    this.setupDragBehavior();

    document.body.appendChild(this.floatButton);
  };

  private setupDragBehavior = () => {
    if (!this.floatButton) return;

    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    this.floatButton.addEventListener('mousedown', (e) => {
      isDragging = true;
      const rect = this.floatButton!.getBoundingClientRect();
      dragOffset.x = e.clientX - rect.left;
      dragOffset.y = e.clientY - rect.top;
      this.floatButton!.style.cursor = 'grabbing';
    });

    document.addEventListener('mousemove', (e) => {
      if (isDragging && this.floatButton) {
        const x = e.clientX - dragOffset.x;
        const y = e.clientY - dragOffset.y;
        
        // 限制在窗口范围内
        const maxX = window.innerWidth - 60;
        const maxY = window.innerHeight - 60;
        
        this.floatButton.style.left = Math.max(0, Math.min(maxX, x)) + 'px';
        this.floatButton.style.top = Math.max(0, Math.min(maxY, y)) + 'px';
        this.floatButton.style.right = 'auto';
        this.floatButton.style.bottom = 'auto';
      }
    });

    document.addEventListener('mouseup', () => {
      if (isDragging && this.floatButton) {
        isDragging = false;
        this.floatButton.style.cursor = 'pointer';
      }
    });
  };

  private showMainInterface = () => {
    if (this.container) {
      // 如果已经显示，则隐藏
      this.hideMainInterface();
      return;
    }

    this.createMainContainer();
    this.renderApp();
    
    // 隐藏悬浮按钮
    if (this.floatButton) {
      this.floatButton.style.display = 'none';
    }
  };

  private hideMainInterface = () => {
    if (this.container) {
      this.container.remove();
      this.container = null;
    }
    
    // 显示悬浮按钮
    if (this.floatButton) {
      this.floatButton.style.display = 'flex';
    }
  };

  private createMainContainer = () => {
    // 创建主容器
    this.container = document.createElement('div');
    this.container.id = 'cyber-menu-matrix';
    this.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
    `;
    
    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        this.hideMainInterface();
        document.removeEventListener('keydown', handleKeydown);
      }
    };
    document.addEventListener('keydown', handleKeydown);
    
    document.body.appendChild(this.container);
  };

  private renderApp = () => {
    if (!this.container) return;
    
    try {
      render(<App onClose={this.hideMainInterface} />, this.container);
    } catch (error) {
      console.error('[Cyber Menu Matrix] ❌ Render failed:', error);
      this.showErrorNotification(error as Error);
    }
  };

  private showErrorNotification = (error: Error) => {
    try {
      if (typeof GM_notification !== 'undefined') {
        GM_notification({
          title: 'Cyber Menu Matrix Error',
          text: `${error.message}\n\nCheck console for details.`,
          timeout: 5000
        });
      } else {
        // 降级到原生通知
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Cyber Menu Matrix Error', {
            body: error.message,
            icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2IDJMMTAgNVYxOEwxNiAyMUwyMiAxOFY1TDE2IDJaIiBmaWxsPSIjRkYwMDZFIiBvcGFjaXR5PSIwLjgiLz4KPHN2Zz4K'
          });
        } else {
          // 最后的降级方案
          console.error('[Cyber Menu Matrix] Error notification:', error.message);
        }
      }
    } catch (notificationError) {
      console.error('[Cyber Menu Matrix] Failed to show notification:', notificationError);
    }
  };

  destroy = () => {
    if (this.container) {
      this.container.remove();
      this.container = null;
    }
    
    if (this.floatButton) {
      this.floatButton.remove();
      this.floatButton = null;
    }
    
    this.isInitialized = false;
    console.log('[Cyber Menu Matrix] 🗑️ Destroyed');
  };

  get version() {
    return '2.0.77';
  }
}

// 导出到全局
const instance = new CyberMenuMatrix();
window.CyberMenuMatrix = instance;

// 在开发环境直接初始化，生产环境由构建配置处理
if (typeof __DEV__ !== 'undefined' && __DEV__) {
  instance.init();
} 