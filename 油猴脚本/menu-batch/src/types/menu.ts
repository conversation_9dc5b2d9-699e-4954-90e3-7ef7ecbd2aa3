// 菜单项数据类型定义
export interface MenuItem {
  // 核心标识字段
  id: string | number;
  name: string;
  code: string;
  children?: MenuItem[];

  // 路由相关字段（按优先级排序）
  routingUrl?: string;        // 路由地址（最高优先级）
  activeRule?: string;        // 资源地址/权限验证路径
  activeRule2?: string;       // 辅助资源地址
  url?: string;              // URL地址
  path?: string;             // 路径

  // 显示配置字段
  displayLocation?: DisplayLocation;  // 交互方式/打开方式
  iconClass?: string;                // 菜单图标CSS类名
  sortNo?: number | string;          // 排序号

  // 状态控制字段
  menu?: boolean;            // 是否为菜单项
  deleteFlag?: boolean;      // 是否已删除/隐藏
  commonModule?: string;     // 是否为常用模块 ("0" | "1")
  injectToken?: boolean;     // 是否注入Token
  checkPermission?: boolean; // 是否需要权限验证
  memo?: string;             // 备注说明

  // 扩展字段
  [key: string]: any;
}

// 打开方式枚举
export type DisplayLocation = '_micro' | '_iframe' | '_blank' | '_self';

// 用户权限类型
export interface UserInfo {
  id: string;
  name?: string;
  roleList?: Array<{
    code: string;
    name?: string;
  }>;
  [key: string]: any;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code?: number;
  message?: string;
  data?: T;
  success?: boolean;
}

// 菜单权限接口响应
export type MenuApiResponse = ApiResponse<MenuItem[]>;

// 智能路由匹配结果
export interface RouteMatchResult {
  item: MenuItem;
  matchType: 'exact' | 'prefix' | 'wildcard' | 'fuzzy';
  matchField: string;
  score: number;
}

// 搜索过滤选项
export interface SearchOptions {
  query: string;
  fields?: string[];
  caseSensitive?: boolean;
  regex?: boolean;
}

// 批量操作选项
export interface BatchUpdateOptions {
  field: keyof MenuItem;
  value: any;
  selectedIds?: string[];
}

// 数据持久化存储结构
export interface StorageData {
  original: MenuItem[];
  modified: MenuItem[];
  metadata: {
    lastFetch: number;
    version: string;
    changes: ChangeRecord[];
  };
  preferences: {
    autoSave: boolean;
    showDeleted: boolean;
    theme: string;
  };
}

// 变更记录
export interface ChangeRecord {
  id: string;
  field: string;
  oldValue: any;
  newValue: any;
  timestamp: number;
  user?: string;
}

// 组件 Props 类型
export interface MenuCardProps {
  item: MenuItem;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onEdit: (item: MenuItem) => void;
  className?: string;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
  placeholder?: string;
}

export interface ModuleTreeProps {
  items: MenuItem[];
  selectedModule?: string;
  onSelect: (moduleId: string) => void;
  expandedNodes?: Set<string>;
  onToggleExpand: (nodeId: string) => void;
}

// 应用状态类型
export interface AppState {
  isVisible: boolean;
  isLoading: boolean;
  error: string | null;
  currentPath: string;
}

// Tampermonkey 全局API类型声明
declare global {
  const GM_setValue: (key: string, value: any) => void;
  const GM_getValue: (key: string, defaultValue?: any) => any;
  const GM_deleteValue: (key: string) => void;
  const GM_listValues: () => string[];
  const GM_setClipboard: (text: string) => void;
  const GM_notification: (options: {
    title: string;
    text: string;
    timeout?: number;
    image?: string;
  }) => void;
  const unsafeWindow: Window;
} 