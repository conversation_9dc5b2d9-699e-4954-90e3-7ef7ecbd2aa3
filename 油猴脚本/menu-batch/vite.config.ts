import { defineConfig } from 'vite';
import preact from '@preact/preset-vite';
import { resolve } from 'path';
import { readFileSync } from 'fs';

export default defineConfig({
  plugins: [
    preact(),
    // 自定义插件：添加油猴头部
    {
      name: 'tampermonkey-header',
      generateBundle(options, bundle) {
        try {
          // 读取头部模板
          const header = readFileSync('./scripts/tampermonkey-header.js', 'utf8');
          
          // 为每个输出文件添加头部
          Object.keys(bundle).forEach(fileName => {
            const file = bundle[fileName];
            if (file.type === 'chunk') {
              file.code = header + '\n' + file.code;
            }
          });
        } catch (error) {
          console.warn('⚠️ Could not read tampermonkey header, skipping...');
        }
      }
    }
  ],
  
  define: {
    // 开发环境变量
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '2.0.77'),
  },
  
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
      ],
    },
  },
  
  build: {
    // 输出单个文件
    lib: {
      entry: resolve(__dirname, 'src/main.tsx'),
      name: 'CyberMenuMatrix',
      fileName: () => 'menu-batch-editor.user.js',
      formats: ['iife'],
    },
    
    rollupOptions: {
      output: {
        // 确保所有代码都内联到单个文件中
        inlineDynamicImports: true,
        manualChunks: undefined,
        
        // 自定义IIFE包装
        format: 'iife',
        name: 'CyberMenuMatrix',
        
        // 添加严格模式和自执行
        intro: '"use strict";',
        outro: 'CyberMenuMatrix.init();',
      },
      
      // 不打包external库（如果有的话）
      external: [],
    },
    
    // 生产环境优化
    minify: process.env.NODE_ENV === 'production' ? 'terser' : false,
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info'],
      },
      mangle: {
        // 保留某些函数名（如果需要的话）
        reserved: ['CyberMenuMatrix'],
      },
    },
    
    // 输出目录
    outDir: 'dist',
    emptyOutDir: true,
  },
  
  // 开发服务器
  server: {
    port: 3000,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  
  // 路径别名
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types'),
    },
  },
}); 