# 🚀 Cyber Menu Matrix

赛博朋克风格的菜单权限批量管理工具，支持智能路由匹配和批量编辑。

![Version](https://img.shields.io/badge/version-2.0.77-00FFFF)
![License](https://img.shields.io/badge/license-MIT-39FF14)
![Tampermonkey](https://img.shields.io/badge/tampermonkey-compatible-FF006E)

## ✨ 特性

- 🎨 **赛博朋克UI设计** - 霓虹发光效果、矩阵动画、故障艺术
- 🎯 **智能路由匹配** - 首次加载时自动定位到与当前页面路由匹配的模块
- ⚡ **批量编辑功能** - 支持批量修改 activeRule 字段值
- 💾 **数据持久化** - 本地存储配置，支持刷新/重置
- 🔍 **实时搜索过滤** - 支持多字段搜索和正则表达式
- 🌐 **多权限支持** - 自动识别管理员和普通用户权限
- 📱 **响应式设计** - 适配桌面和移动设备

## 🏗️ 技术栈

- **框架**: Preact 10.x (3KB gzipped)
- **语言**: TypeScript
- **样式**: Tailwind CSS + 自定义CSS
- **状态管理**: Zustand
- **构建工具**: Vite + Rollup
- **图标**: Lucide Preact
- **运行环境**: Tampermonkey

## 🚀 快速开始

### 开发环境

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 在浏览器中打开 http://localhost:3000
```

### 构建生产版本

```bash
# 构建开发版本（保留日志）
npm run build:dev

# 构建生产版本（压缩优化）
npm run build:prod
```

构建完成后，在 `dist/` 目录中会生成 `menu-batch-editor.user.js` 文件。

### 安装到Tampermonkey

1. 复制 `dist/menu-batch-editor.user.js` 的内容
2. 在Tampermonkey中创建新脚本
3. 粘贴代码并保存
4. 访问目标网站，右下角会出现⚙️悬浮按钮

## 📖 使用说明

### 基本操作

1. **打开界面**: 点击右下角的⚙️悬浮按钮
2. **搜索过滤**: 在搜索框中输入关键词
3. **选择菜单项**: 点击菜单项进行选择
4. **批量编辑**: 选择多个项目后，在底部输入新值并点击SET
5. **导出数据**: 点击COPY按钮导出JSON到剪贴板

### 交互操作

| 操作 | 效果 |
|------|------|
| **单击** | 选择单个菜单项 |
| **Ctrl+点击** | 多选切换 |
| **双击** | 编辑详情（TODO） |
| **拖拽** | 范围选择（TODO） |
| **ESC键** | 关闭界面 |

### 智能路由匹配

脚本会自动分析当前页面路径，智能匹配相关的菜单项：

- **精确匹配**: 路径完全一致 🔥🔥🔥
- **前缀匹配**: 当前路径以菜单路径开头 🔥🔥
- **通配符匹配**: 支持`*`通配符 🔥
- **模糊匹配**: 包含关键路径段 ⭐

## 🎯 API接口

脚本根据用户权限自动选择接口：

**管理员用户**:
```
${baseUrl}/dxdsapi/PrivilegeProduct/privilegeTree/1?projectCode=1
```

**普通用户**:
```
${baseUrl}/dxdsapi/PrivilegeProduct/myPrivilege/1
```

### 权限判断逻辑

从 `localStorage.user` 中获取用户信息：
- `user.id === '1'` 或
- `user.roleList` 中存在 `code === 'administrator'` 的角色

## 📁 项目结构

```
src/
├── main.tsx                        # 入口文件
├── App.tsx                         # 主应用组件
├── components/                     # UI组件
│   ├── CyberPanel.tsx             # 主面板组件
│   ├── ModuleTree.tsx             # 模块树组件（TODO）
│   ├── MenuList.tsx               # 菜单列表组件（TODO）
│   └── SearchBar.tsx              # 搜索栏组件（TODO）
├── stores/                        # Zustand状态管理
│   └── menuStore.ts               # 菜单状态
├── types/                         # TypeScript类型
│   └── menu.ts                    # 菜单类型定义
└── styles/                        # 样式文件
    └── globals.css                # 全局样式
```

## 🎨 UI设计语言

### 色彩系统

| 元素 | 颜色代码 | 用途 |
|------|----------|------|
| 主色 | `#00FFFF` | 边框、文字 |
| 辅色 | `#FF006E` | 选中状态 |
| 强调 | `#39FF14` | 多选、激活 |
| 警告 | `#FFD700` | 警告状态 |
| 背景 | `#0A0A0A` | 主背景 |
| 面板 | `#1A1A2E` | 面板背景 |

### 状态指示器

| 符号 | 含义 | 颜色 |
|------|------|------|
| `▣` | 菜单节点 | 霓虹青 |
| `[●]` | 激活状态 | 矩阵绿 |
| `[○]` | 暂停状态 | 灰色 |
| `[✕]` | 删除状态 | 红色 |
| `📍` | 当前页面匹配 | 金黄 |

## 🔧 开发指南

### 环境变量

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_DEBUG_MODE=true

# .env.production  
VITE_API_BASE_URL=https://your-api.com
VITE_DEBUG_MODE=false
```

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 配置
- 组件使用函数式编程
- CSS 优先使用 Tailwind 类名
- 复杂动画使用自定义 CSS

### 构建配置

构建过程会自动：
1. 编译 TypeScript
2. 处理 CSS（Tailwind + PostCSS）
3. 压缩和优化代码
4. 添加 Tampermonkey 头部
5. 内联所有资源到单文件

## 🐛 故障排除

### 常见问题

1. **脚本无法加载**
   - 检查 Tampermonkey 是否启用
   - 确认 @match 规则是否正确

2. **API 请求失败**
   - 检查网络连接
   - 确认用户登录状态
   - 查看浏览器控制台错误

3. **界面显示异常**
   - 清除浏览器缓存
   - 检查是否有其他脚本冲突
   - 更新到最新版本

### 调试模式

在开发环境中，脚本会输出详细日志：
```javascript
console.log('[Cyber Menu Matrix] Debug info...');
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Pull Request 和 Issue！

## 📞 支持

- [GitHub Issues](https://github.com/your-username/cyber-menu-matrix/issues)
- [讨论区](https://github.com/your-username/cyber-menu-matrix/discussions)

---

**⚡ Made with 💾 and 🎮 in Cyberpunk 2077 style** 