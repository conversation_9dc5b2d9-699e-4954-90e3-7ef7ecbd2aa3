// ==UserScript==
// @name         自动给登录界面填充加密后的账号密码
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Encrypt account and password using RSA for specific URLs and auto fill form on button click
// <AUTHOR> Name
// @match        */dxdsapi/login.html
// @rexxxxquire      https://unpkg.com/jsencrypt@3.0.0-beta.1/bin/jsencrypt.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js
// @grant        none
// ==/UserScript==


(function () {
  'use strict';
  const init = () => {
    if (!JSEncrypt) {
      // if (!JSEncrypt || typeof JSEncrypt.JSEncrypt !== 'function') {
      console.log(123, JSEncrypt)
      setTimeout(() => {
        init()
      }, 600);
      return
    }
    const pubKey =
      'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeVp+B5QC2io6RJYx5NtMryiE02yIRVi0Ead2Oaklfo6cc/vBoSh35oxVDA2WSfgP5JwZ74DqRHFHt5LbXVnR//CebiHrvaFfcF31TNIriDtvitGqGaONbzRSYjI0t4OVHUtR1MGXtD+yCuXnLnZJE8Rnn+c5YHROG+AH3gV6t1wIDAQAB';
    const styleContent = `

      ul.account-list {
        font: 300 1em / 1.8 PingFang SC, Lantinghei SC, Microsoft Yahei, Hiragino Sans GB, Microsoft Sans Serif, WenQuanYi Micro Hei, sans-serif;
        position: fixed;
        border: 1px solid rgb(104 104 104 / 6%);
        border-radius: 10px;
        box-shadow: 0 6px 16px -8px #********, 0 9px 28px #0000000d, 0 12px 48px 16px #********;
        padding: 5px;
        top: 135px;
        left: 10px;
        list-style: none;
        width: 854px;
        display: flex;
        flex-wrap: wrap;
        gap: 1px;
        font-family: Avenir, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
        box-shadow: 0 20px 20px -20px;
      }

      li.account-item {
        line-height: 20px;
        font-size: 14px;
        padding: 15px 10px;
        width: 150px;
        cursor: pointer;
        text-align: center;
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      li.account-item.active {
          background-color: var(--jjext-color-brand-fill2-hover);
      }

      li.account-item:hover {
          background-color: var(--jjext-color-brand-fill2-hover);
          font-size: 15px;
          font-weight: 900;
          transition: all .5s;
      }

        /* 设置选中高亮效果 */
        .list-item.active {
            background-color: #0d6efd;
            color: white;
        }
        /* 调整头像大小 */
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 10px;
        }
      `
    function rsaEncrypt(value) {
      //   const encryptor = new JSEncrypt.JSEncrypt(); // 创建加密对象实例
      const encryptor = new JSEncrypt(10); // 创建加密对象实例
      encryptor.setPublicKey(pubKey); // 设置公钥
      return encryptor.encrypt(value);
    }


    const url = window.location.href;
    const ipPattern1 = /^https?:\/\/172\.168\.\d+\.\d+/;
    const ipPattern2 = /^https?:\/\/117\.156\.\d+\.\d+/;
    const pathPattern1 = /\/account\/login/;
    const pathPattern2 = /\/dxdsapi\/login\.html/;

    let account = "developer";
    let password = "1qaz@WSX";

    const accountList = [
      { account: 'developer_da', password: '1qaz@WSX', role: '普通' },
      { account: 'developer_da_2', password: '1qaz@WSX', role: '两票监护人一' },
      { account: 'developer_da_6', password: '1qaz@WSX', role: '两票监护人二' },
      { account: 'developer_da_3', password: '1qaz@WSX', role: '值班负责人' },
      { account: 'developer_da_4', password: '1qaz@WSX', role: '值长' },
      { account: 'developer_da_5', password: '1qaz@WSX', role: '监察人员' },
      { account: 'developer', password: '1qaz@WSX' },
      { account: 'admin', password: 'NWH11qaz@WSX' },
      { account: 'zyadmin', password: 'NWH11qaz@WSX' },
      { account: 'ZZGTest1', password: 'ZZGTest1' },
      { account: 'Lk123456', password: 'Lk123456' },
      { account: 'Test10', password: 'Test10' },
      { account: 'mr_test1', password: 'QAZwsx12345' },
      { account: 'zhangyue', password: 'QAZwsx12345' },
      { account: 'ai_tingyan', password: 'QAZwsx12345' },
      { account: 'leitongda', password: 'ABCabc123456' },
      { account: 'li_xiaohu', password: 'Ctg111111' },
      { account: 'li_zhiliang', password: 'QAZwsx12345' },
      { account: 'long_tan', password: 'QAZwsx12345' },
      { account: 'ma_qiang', password: 'QAZwsx12345' },
      { account: 'wang_xiuwei', password: 'QAZwsx12345' },
      { account: 'xu_li', password: 'QAZwsx12345' },
      { account: 'yang_jicheng', password: 'QAZwsx12345' },
      { account: 'yao_pujie', password: 'QAZwsx12345' },
    ]


    if (ipPattern1.test(url) || ipPattern2.test(url) || pathPattern1.test(url) || pathPattern2.test(url)) {

      console.log(ipPattern1.test(url), ipPattern2.test(url), pathPattern1.test(url), pathPattern2.test(url))
      // Encrypt account and password
      function encryptAccountPassword(account, password, publicKey) {

        const encryptedAccount = encodeURIComponent(rsaEncrypt(account) || '');
        const encryptedPassword = encodeURIComponent(rsaEncrypt(password) || '');

        return { encryptedAccount, encryptedPassword };
      }

      // console.log("Public Key:", pubKey);

      const form = document.querySelector('form');
      const submitButton = form?.querySelector('button[type="submit"]');
      const autoFillButton = document.createElement('button');
      autoFillButton.className = 'auto-fill-btn btn btn-danger w-50';
      autoFillButton.id = 'auto-fill-btn';
      autoFillButton.type = 'button';
      autoFillButton.textContent = '账号：' + account + ' 【自动填写表单】';

      submitButton.parentElement.className = 'd-flex gap-2';
      submitButton?.parentElement?.appendChild(autoFillButton);

      // Button click event
      autoFillButton.addEventListener('click', () => {

        const { encryptedAccount, encryptedPassword } = encryptAccountPassword(account, password, pubKey);
        console.log("🚀 ~ button.addEventListener ~ account, password:", account, password)

        // console.log("Private Key:", privateKey);  // Do not share the private key!
        console.log("Encrypted Account:", encryptedAccount);
        console.log("Encrypted Password:", encryptedPassword);
        // Find the form fields (modify the selectors as per your form structure)
        const accountField = document.querySelector('input[name="j_username"], input[name="account"]');
        const passwordField = document.querySelector('input[name="j_password"]');

        if (accountField && passwordField) {
          accountField.value = encryptedAccount;
          passwordField.value = encryptedPassword;
          // alert('Form fields have been auto-filled with encrypted values.');
        } else {
          // alert('Form fields not found!');
        }
      });


      const userListContainer = document.createElement('div');
      userListContainer.className = 'container mt-2';
      const userList = document.createElement('ul');
      userList.className = 'account-lisxt row g-3 p-0';
      userList.id = 'userList';

      accountList.forEach(user => {


        const col = document.createElement('li');
        col.className = 'col-6 col-md-4 col-lg-3 col-xl-3';
        col.style = 'list-style: none;';
        col.dataset.account = user.account;

        const listItem = document.createElement('div');
        listItem.className = 'list-item p-2 d-flex align-items-center border rounded-3 border-warning-subtle bg-warning-subtle';
        listItem.onclick = (e) => {
          console.log(user, e)

          // 移除其他兄弟 li 上的 active 类
          Array.from(userList.children).forEach(li => {
            li.children[0].classList.remove('border-success-subtle');
            li.children[0].classList.remove('bg-success-subtle');
            li.children[0].classList.add('border-warning-subtle');
            li.children[0].classList.add('bg-warning-subtle');
            if (li.dataset.account === user.account) {
              li.children[0].classList.add("border-success-subtle")
              li.children[0].classList.add("bg-success-subtle")
              li.children[0].classList.remove("border-warning-subtle")
              li.children[0].classList.remove("bg-warning-subtle")
            }
          });

          password = user.password;
          account = user.account;
          console.log('account Password:', account, password);

          autoFillButton.textContent = '账号：' + account + ' 【自动填写表单】';

          // 给当前点击的 li 添加 active 类
          listItem.classList.add('active ');

        }

        const avatar = document.createElement('img');
        avatar.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAIAAAADnC86AAABU0lEQVR4nO3WP4+CMBgG8BdaXBr/JZCaMOjCon7/T+FkMIaQoAmUGGCSaAik7Q3kOI+cRCdveN+ptDzPr2ON3W4HnxjzIyrCCCOMMMIII/xf4aZpfN9vmgYAhBD7/T4IgqqqXmx8jA809GGlVBzHhBAAKMvyfr9vNhvOeZIkr6iP8eGGPpwkCeecUgoAt9ttPp8TQmazWV3XWuv2n6IoTqcTAJzP5zzPn8UHGvpwlmWMMcZY+yml7CpM01RKtWvbtrXWURQppRzHeRYfaAAA+ggXRVHXdRzHAHA4HBaLhZSyqzDNn1tyzsMw9DxvIL7dbgkhzxp+wev1ul2EYbharaqqulwu0+m0LMvRaGQYRnuqtRZCuK4rhPA8r9vvxSmljLE/G/pwb8bj8fV6PR6PlmUtl8tuP03TyWTiOI6UMk1T13XfbQAAAx/0CCOMMMIII/w9XxQt4FPrpYzlAAAAAElFTkSuQmCC";
        avatar.alt = 'User Avatar';
        avatar.className = 'user-avatar';

        const userInfo = document.createElement('div');
        const username = document.createElement('h5');
        username.className = 'mb-0';
        username.textContent = user.role || user.account;

        const accountDom = document.createElement('small');
        accountDom.textContent = `密码：${user.password}`;

        const role = document.createElement('small');
        role.textContent = `角色：${user.role}`;

        // 组装列表项
        userInfo.appendChild(username);
        // userInfo.appendChild(document.createElement('br'));
        userInfo.appendChild(accountDom);
        // userInfo.appendChild(document.createElement('br'));
        // userInfo.appendChild(role);

        listItem.appendChild(avatar);
        listItem.appendChild(userInfo);
        col.appendChild(listItem);
        userList.appendChild(col);

        // const li = document.createElement('li');
        // li.className = account === item.account ? 'account-item active' : 'account-item';
        // li.dataset.password = item.password;
        // li.dataset.account = item.account;
        // li.textContent = item.label ? `${item.label}(${item.account})` : item.account;
        // userList.appendChild(li);
      })

      userListContainer.appendChild(userList);
      document.body.appendChild(userListContainer);

      const style = document.createElement('style');
      style.textContent = styleContent;

      document.head.appendChild(style);


      userList.addEventListener('clicxk', function (event) {
        const target = event.target;
        console.log('click', target);
        // 检查点击的是否是 li 元素
        if (target.tagName === 'LI') {
          // 获取并打印 dataset 的 password 属性值
          password = target.dataset.password;
          account = target.dataset.account;
          console.log('Password:', account, password);

          // 给当前点击的 li 添加 active 类
          target.classList.add('active ');

          // 移除其他兄弟 li 上的 active 类
          Array.from(userList.children).forEach(li => {
            if (li !== target) {
              li.classList.remove('active');
            }
          });
        }
      });

    } else {
      console.log("URL does not match specified patterns.");
    }
  }
  // Wait for the entire page and required scripts to load
  window.addEventListener('load', () => {
    console.log('init')
    // Check if the current URL matches the specified patterns
    init()
  });
})();
