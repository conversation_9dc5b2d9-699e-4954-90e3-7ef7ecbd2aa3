# 菜单权限管理器 - 油猴脚本

## 功能介绍

这是一个用于管理系统菜单权限的油猴脚本，主要功能包括：

1. **自动拦截菜单权限接口** - 监听 `/dxdsapi/PrivilegeProduct/myPrivilege` 接口调用
2. **可拖动悬浮按钮** - 页面右下角显示设置按钮，支持拖拽移动
3. **菜单管理界面** - 弹框显示菜单列表和JSON数据
4. **activeRule编辑** - 支持修改菜单项的activeRule值
5. **数据重置功能** - 可恢复原始数据

## 安装方法

1. 安装 [Tampermonkey](https://www.tampermonkey.net/) 浏览器扩展
2. 复制 `menu-privilege-manager.user.js` 文件内容
3. 在Tampermonkey中创建新脚本，粘贴代码
4. 保存并启用脚本

## 使用说明

### 1. 悬浮按钮
- 脚本加载后，页面右下角会出现一个齿轮图标的悬浮按钮
- 按钮可以拖拽到页面任意位置
- 点击按钮打开管理界面

### 2. 管理界面

#### 左侧菜单列表
- 显示所有菜单项的层级结构
- 每个菜单项显示：名称、ID、Code、ActiveRule
- **单选模式**：点击菜单项可选中（粉红色高亮）
- **多选模式**：
  - Ctrl/Cmd + 点击：切换选择状态
  - 鼠标拖拽：按住鼠标左键拖动可选择区域内的所有菜单项（绿色高亮）
- 支持混合选择模式

#### 右侧操作区域
- **输入框**：用于输入新的activeRule值
  - 单选时：显示选中项的当前activeRule值
  - 多选时：显示选中项数量，可批量设置activeRule
- **设置按钮**：将输入框的值应用到选中的菜单项（支持批量操作）
- **重置按钮**：恢复所有数据到原始状态并清除选择
- **JSON编辑器**：始终显示完整的菜单数据JSON
- **复制按钮**：复制完整JSON数据到剪贴板

### 3. 操作流程

#### 单项操作
1. 访问包含菜单权限接口的页面
2. 等待接口调用完成（脚本会自动捕获数据）
3. 点击悬浮按钮打开管理界面
4. 在左侧点击选择要修改的菜单项
5. 在输入框中输入新的activeRule值
6. 点击"设置"按钮应用更改

#### 批量操作
1. 使用以下方式选择多个菜单项：
   - **拖拽选择**：按住鼠标左键拖动选择区域
   - **Ctrl/Cmd点击**：按住Ctrl(Windows)或Cmd(Mac)键点击多个项目
2. 在输入框中输入要批量设置的activeRule值
3. 点击"设置"按钮批量应用更改
4. 系统会显示成功更新的项目数量

#### 数据管理
- **重置**：点击"重置"按钮恢复原始数据并清除所有选择
- **JSON查看**：右侧始终显示完整的菜单数据结构
- **数据复制**：点击"复制JSON"按钮复制完整数据到剪贴板

## 技术特性

- **接口拦截**：同时支持 fetch 和 XMLHttpRequest
- **数据备份**：自动保存原始数据，支持重置
- **类型安全**：添加了运行时类型检查
- **用户体验**：现代化UI设计，支持拖拽和响应式布局
- **错误处理**：完善的错误提示和边界情况处理

## 注意事项

1. 脚本需要在包含目标接口的页面上运行
2. 首次使用需要等待接口调用以捕获菜单数据
3. 修改的数据仅在当前会话中有效，刷新页面会重置
4. 建议在测试环境中使用，避免影响生产数据

## 兼容性

- 支持所有现代浏览器
- 兼容 Tampermonkey 和 Greasemonkey
- 适用于任何使用该接口结构的系统

## 更新日志

### v1.1
- 🎨 **蓝色赛博朋克风格**：全新的霓虹蓝色主题设计
- 🖱️ **多选功能**：支持拖拽选择和Ctrl/Cmd点击多选
- 📊 **批量操作**：可同时修改多个菜单项的activeRule
- 🔍 **JSON显示优化**：右侧始终显示完整菜单数据
- ✨ **视觉效果增强**：发光效果、动画和渐变背景

### v1.0
- 初始版本
- 基础菜单管理功能
- 悬浮按钮和拖拽支持
- activeRule编辑功能
- 数据重置功能 